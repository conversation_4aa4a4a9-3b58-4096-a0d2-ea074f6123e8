// https://taro-docs.jd.com/taro/docs/config
const path = require('path');
const env = process.env.fmt_env || 'dev';
const name = process.env.npm_package_name;
const config = {
  projectName: 'lingzhao-candidate-weapp',

  date: '2022-3-5',
  designWidth: 375,
  deviceRatio: {
    640: 2.34 / 2,
    750: 1,
    828: 1.81 / 2,
    375: 2,
  },
  sourceRoot: 'src',
  outputRoot: 'dist',

  plugins: ['@tarojs/plugin-html'],
  copy: {
    patterns: [
      // { from: 'src/', to: 'dist/', ignore: ['*.js'] }, // 指定需要 copy 的目录
      { from: 'src/ext.json', to: 'dist/ext.json' }, // 指定需要 copy 的文件
    ],
  },
  framework: 'react',
  // 全局变量
  defineConstants: {
    FMT_ENV: `'${env}'`,
    FMT_NAME: `'${name}'`,
  },
  alias: {
    '@components': path.resolve(__dirname, '..', 'src/components'),
    '@styles': path.resolve(__dirname, '..', 'src/styles'),
    '@utils': path.resolve(__dirname, '..', 'src/utils'),
    '@landrover': path.resolve(__dirname, '..', 'src/landrover'),
    '@images': path.resolve(__dirname, '..', 'src/images'),
    '@services': path.resolve(__dirname, '..', 'src/services'),
    '@stores': path.resolve(__dirname, '..', 'src/stores'),
  },
  mini: {
    optimizeMainPackage: {
      enable: true,
    },
    postcss: {
      pxtransform: {
        enable: true,
        config: {},
      },
      url: {
        enable: true,
        config: {
          limit: 1024, // 设定转换尺寸上限
        },
      },
      cssModules: {
        enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]',
        },
      },
    },
    miniCssExtractPluginOption: {
      ignoreOrder: true,
    },
  },
  h5: {
    publicPath: '/',
    staticDirectory: 'static',
    postcss: {
      autoprefixer: {
        enable: true,
        config: {},
      },
      cssModules: {
        enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]',
        },
      },
    },
  },
};

module.exports = function(merge) {
  if (process.env.NODE_ENV === 'development') {
    return merge({}, config, require('./dev'));
  }
  return merge({}, config, require('./prod'));
};
