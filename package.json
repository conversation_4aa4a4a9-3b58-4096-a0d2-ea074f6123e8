{"name": "l<PERSON><PERSON><PERSON>-candidate-weapp", "version": "1.0.0", "private": true, "description": "候选人端小程序", "templateInfo": {"name": "default", "typescript": true, "css": "sass"}, "scripts": {"build": "taro build --type weapp", "dev": "npm run build -- --watch", "build:prd": "fmt_env=prd npm run build", "upload": "npm run build:prd && npx weapp-upload", "deploy": "curl --header 'Content-Type: application/json' --request POST --data '{}' https://flow-openapi.aliyun.com/pipeline/webhook/7rdolM0WRGkuplAUFZt8"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "dependencies": {"@tarojs/cli": "3.4.3", "@tarojs/components": "3.4.3", "@tarojs/plugin-html": "^3.4.3", "@tarojs/react": "3.4.3", "@tarojs/taro": "3.4.3", "crypto-js": "^4.2.0", "dayjs": "^1.10.7", "react": "^17.0.0", "react-dom": "^17.0.0", "react-redux": "^7.2.6", "redux": "^4.0.0", "redux-logger": "^3.0.6", "redux-thunk": "^2.3.0", "taro-canvas": "^0.0.3", "weapp-upload": "^1.0.1"}, "devDependencies": {"@babel/core": "^7.8.0", "@babel/runtime": "^7.7.7", "@tarojs/plugin-doctor": "^0.0.18", "@types/classnames": "^2.3.1", "@types/crypto-js": "^4.2.2", "@types/prop-types": "^15.7.4", "@types/react": "^17.0.2", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "babel-preset-taro": "3.4.3", "eslint": "^6.8.0", "eslint-config-taro": "3.4.3", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "typescript": "^4.1.0"}}