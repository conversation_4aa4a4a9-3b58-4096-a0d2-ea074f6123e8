{"miniprogramRoot": "dist/", "projectname": "l<PERSON><PERSON><PERSON>-candidate-weapp", "description": "候选人端小程序", "appid": "wxfded51e364b89d6f", "setting": {"urlCheck": true, "scopeDataCheck": false, "coverView": true, "es6": false, "postcss": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "minified": true, "autoAudits": false, "newFeature": false, "uglifyFileName": false, "uploadWithSourceMap": true, "useIsolateContext": true, "nodeModules": false, "enhance": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "ignoreUploadUnusedFiles": true}, "compileType": "miniprogram", "condition": {}, "libVersion": "3.4.3", "packOptions": {"ignore": [], "include": []}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "srcMiniprogramRoot": "dist/"}