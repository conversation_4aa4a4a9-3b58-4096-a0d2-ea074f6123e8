import { Image, View, WebView } from '@tarojs/components';
import Taro, { getCurrentInstance } from '@tarojs/taro';
import type { FC } from 'react'

type ComponentNameProps = {}
const ComponentName: FC<ComponentNameProps> = () => {
  const { extConfig } = Taro.getApp().$app;
  const { router } = getCurrentInstance();
  const url = router?.params?.url || '';

  if(url.indexOf('wx.vzan')>-1){
    return <View style={{
      display:'flex',
      justifyContent:'center',
      alignItems: 'center'
    }}>
      <View style={{marginTop:'50px'}}>
      <Image style={{
        border:'solid 2px #eee',
        width: '240px',
        height: '240px'
      }} mode="aspectFit" showMenuByLongpress={true} src="https://oss.lingzhao.net/common/pezb.png" />
      <View style={{textAlign:'center',paddingTop:'8px'}}>长按识别小程序码看直播</View>
      </View>
      
    </View>
  }
  return (
    <WebView src={`${extConfig.domain}/api/system/proxy?url=${url}`} ></WebView>
  );
}
export default ComponentName