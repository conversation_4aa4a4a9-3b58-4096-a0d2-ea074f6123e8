import type { FC } from 'react'
import { ui } from "@landrover/business";
import Taro, { getCurrentInstance } from '@tarojs/taro';

type ComponentNameProps = {}
const ComponentName: FC<ComponentNameProps> = () => {
  const { router } = getCurrentInstance();
  const { token, title = '信息收集表' } = router?.params || {};
  Taro.setNavigationBarTitle({ title });
  // @ts-ignore
  return <gd-form token={token} onSubmit={async (res: any) => {
    if (res.detail === 'success') {
      await ui.showToast('提交成功');
      setTimeout(() => Taro.navigateBack(), 1000);
    } else {
      await ui.showToast('提交失败');
    }
  }}
  />;
}
export default ComponentName;
