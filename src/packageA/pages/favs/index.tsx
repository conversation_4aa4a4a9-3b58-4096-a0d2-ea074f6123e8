import type { FC } from 'react'
import { useEffect, useState } from 'react'
import { container } from '@landrover/business/index'
import Taro, { useDidShow, useReachBottom } from '@tarojs/taro'
import { View } from '@tarojs/components'
import Loading from '@components/Loading'
import Empty from '@components/Empty'
import NoMore from '@components/NoMore'
import FavPositionCard from '@components/FavPositionCard'
import './index.scss'


type FavsProps = {}
const Favs: FC<FavsProps> = () => {
  const [loading, setLoading] = useState<boolean>(true)
  const [scrollComplete, setScrollComplete] = useState<boolean>(false)
  const [pageIndex, setPageIndex] = useState<number>(1)
  const [totalNumber, setTotalNumber] = useState<number>(0)
  const [listData, setListData] = useState<FavsTypes[]>([])

  Taro.setBackgroundTextStyle({
    textStyle: 'dark',
  })


  useDidShow(() => {
    getPositionList({
      current: 1,
    })
  })

  const getPositionList = (params, callback?: any) => {
    container.saasService
      .getFavs({
        pageSize: 10,
        ...params,
      })
      .then((result: { list: any[]; current: number; total: number }) => {
        const { current, list, total } = result

        setLoading(false)
        setPageIndex(current)
        setListData(current === 1 ? list : listData.concat(list))
        setTotalNumber(total)
        callback && callback()
      })
  }

  useEffect(() => {
      getPositionList({
        current: 1,
      })
  }, [])

  useReachBottom(() => {
    const getLastPageIndex = Math.ceil(totalNumber / 10)
    const hasNext = pageIndex < getLastPageIndex
    if (!hasNext) {
      setScrollComplete(true)
      return
    }

    const getPageIndex = pageIndex + 1
    getPositionList({
      current: getPageIndex,
    })
  })

  
  if (loading) {
    return <Loading />
  }
  if (!loading && !listData.length) return <Empty message="暂无数据" />
  return (
    <View
    className='container favs-container'>
      {!!listData.length && (
        <View>
          {listData.map((item) => (
            <FavPositionCard
              positionData={item.position}
              key={item.id}
              onCancelFav={() => {
                container.saasService.cancelPositionFavs(item.id).then(() => {
                  const data = listData.filter((subitem)=>subitem.id !== item.id );
                  setListData(data);
                })
                  
              }}
            />
          ))}

          {scrollComplete && <NoMore>没有更多数据了</NoMore>}
        </View>
      )}
    </View>
  )
}
export default Favs
