import { Image, View } from '@tarojs/components';
import { FC, useState, useEffect } from 'react';
import { container } from '@landrover/business/index';
import Taro, { useDidHide } from '@tarojs/taro';
import Result from '@components/qz/Result';
import CustomNav from '@components/CustomNav';
import QzButton from '@components/Button';
import './result.scss';

type ResultPageProps = {};
const ResultPage: FC<ResultPageProps> = () => {
  const { extConfig: { sign, domain } } = Taro.getApp().$app;
  const { sign: userSign, cookie } = container.userService.auth || {};
  const [qrcodeUrl, setQrcodeUrl] = useState<string>('');

  useEffect(() => {
    wx.downloadFile({
      header: {
        cookie,
        'x-scene': 'MINIPROGRAM',
        'x-sign': sign,
      },
      url: `${domain}/api/wechat/qrcode?actionName=QR_STR_SCENE&sceneStr=bind:${userSign}&timestamp=${new Date().getTime()}`,
      success: function (res: any) {
        setQrcodeUrl(res.tempFilePath);
      },
    });
  }, [domain, sign, userSign, cookie]);

  // 离开页面时更新用户信息（更新订阅状态）
  useDidHide(() => container.userService.updateUserProfile());

  return (
    <View>
      <CustomNav />
      <Result
        status="success"
        title="报名成功"
        subTitle="长按关注公众号，我们将实时通知您报名结果"
        extra={
          <>
            <Image style={{ border: 'solid 1px #eee', width: '200px', height: '200px' }} showMenuByLongpress src={qrcodeUrl} />
            <QzButton type="gray" onClick={() => Taro.redirectTo({ url: '/pages/flow/list' })} circle>
              查看报名进展
            </QzButton>
          </>
        }
      />
    </View>
  );
};
export default ResultPage;
