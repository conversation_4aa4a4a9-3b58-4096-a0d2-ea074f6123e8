import { Icon, View } from '@tarojs/components';
import { getCurrentInstance } from '@tarojs/taro';
import type { FC } from 'react'
import './index.scss'

type ErrorProps = {}
const Error: FC<ErrorProps> = () => {
  const { router } = getCurrentInstance();
  const errorCode = router?.params?.errorCode || '-1';
  const errorMessage = decodeURIComponent(router?.params?.errorMessage || '');
  const ERROR_MAP = {
    '1001': {
      title: '该企业尚未开通小程序',
    },
  };
  return (
    <View className="qz-errPage">
      <Icon size='60' type='warn' />
      <View>{ERROR_MAP[errorCode] ? ERROR_MAP[errorCode].title : errorMessage}</View>
    </View>
  )
}
export default Error