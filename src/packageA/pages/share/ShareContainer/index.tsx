import { Block, Image, Swiper, SwiperItem, View } from '@tarojs/components';
import Button from '@components/Button';
import { TaroCanvas } from 'taro-canvas';
import { FC, useEffect, useMemo, useState } from 'react';
import Taro from '@tarojs/taro';
import wxapi from '@utils/wxapp-promise';
import { getPositionBaseInfo, getSalaryText } from '@utils/index';
import { ui, container } from '@landrover/business/index';
import CustomNav from '@components/CustomNav';
import Footer from '@components/Footer';
import { useSelector } from 'react-redux';
import { stringify } from 'querystring';
import './index.scss';

type ShareContainerProps = {};
const ShareContainer: FC<ShareContainerProps> = () => {
  const shareData = useSelector((state: any) => state.share);
  if (!shareData?.params?.id) shareData.params.id = shareData?.position?.id;
  const data = useMemo(() => shareData?.position || {}, [shareData]);
  const {
    name,
    client,
    salaryBegin,
    salaryEnd,
    salaryType,
    degree,
    workYears,
    workType,
    description,
    advantages,
    number,
    city,
    address,
  } = data;
  const { dictMap = {} } = useSelector((state: any) => state.dict);
  const { TPL_CONFIG, extConfig } = Taro.getApp().$app;
  const { nickname } = extConfig;
  const { primaryColor } = TPL_CONFIG.basic;
  const salary = getSalaryText({ salaryBegin, salaryEnd, salaryType }, dictMap);
  const baseInfo = getPositionBaseInfo(
    { city, workYears, degree, number },
    dictMap,
  );
  const [canvasStatus, setCanvasStatus] = useState<boolean>(false);
  const [drawedCount, setDrawedCount] = useState<number>(0);
  const [swiperIndex, setSwiperIndex] = useState<number>(0);
  const [shareImage, setShareImage] = useState<any>([]);
  const [config, setConfig] = useState<any>([]);

  let requirementsTags: any[] = [];
  workType && requirementsTags.push(dictMap?.position?.workType[workType]);
  requirementsTags.push(`${number}人`);
  degree && requirementsTags.push(dictMap?.common?.degree[degree]);
  workYears && requirementsTags.push(dictMap?.position?.workYears[workYears]);
  city && requirementsTags.push(dictMap?.common?.city[city[0]]?.name);

  // 获取小程序码
  const userInfo = container.userService.auth;
  const [qrcodeUrl, setQrcodeUrl] = useState<string>('');

  // 这里使用decodeURIComponent是因为经过stringify之后的shareUser值中的逗号会被转义

  useEffect(() => {
    console.log(extConfig.domain);
    wx.downloadFile({
      header: {
        cookie: userInfo.cookie,
        'x-scene': 'MINIPROGRAM',
        'x-sign': extConfig.sign,
      },
      url: `${extConfig.domain}/api/miniprogram/qrcode?page=${encodeURIComponent(
        '/packageA/pages/position/detail',
      )}&scene=${encodeURIComponent(stringify(shareData?.params))}`,
      success: (res: any) => {
        setQrcodeUrl(res.tempFilePath);
      },
      fail: (res: any) => {
        console.log(res);
      },
    });
  }, [extConfig.domain, extConfig.sign, shareData?.params, userInfo.cookie]);

  useEffect(() => {
    async function creatRssConfig(): Promise<void> {
      const canvasConfig = {
        width: 750 * 2,
        height: 1120 * 2,
        backgroundColor: '#fff',
        debug: false,
        pixelRatio: 5,
      };
      const blocksConfig = [
        {
          x: 40 * 2,
          y: 720 * 2,
          width: 120 * 2,
          height: 120 * 2,
          borderRadius: 20 * 2,
          backgroundColor: '#eee',
          // backgroundColor: "linear-gradient(to right, blue, pink)",
          zIndex: 10,
        }, {
          key: 'company-backgroundColor',
          x: 40 * 2,
          y: 720 * 2,
          width: 120 * 2,
          height: 120 * 2,
          borderRadius: 20 * 2,
          backgroundColor: primaryColor,
          zIndex: 10,
        },
      ];
      const textsConfig = [
        {
          x: 40 * 2,
          y: 48 * 2,
          text: name,
          fontSize: 44 * 2,
          color: '#000',
          lineHeight: 68 * 2,
          width: 670 * 2,
          zIndex: 999,
          fontWeight: 'bold',
        },
        {
          x: 40 * 2,
          y: 120 * 2,
          text: salary,
          fontSize: 40 * 2,
          color: '#FE3925',
          lineHeight: 56 * 2,
          width: 670 * 2,
          zIndex: 999,
        },
        {
          x: 40 * 2,
          y: 180 * 2,
          text: baseInfo.map(item => item.value).join(' · '),
          fontSize: 28 * 2,
          color: '#666666',
          lineHeight: 40 * 2,
          width: 670 * 2,
          zIndex: 999,
        },
        {
          x: 40 * 2,
          y: 370 * 2,
          text: '岗位描述',
          fontSize: 30 * 2,
          color: '#000',
          lineHeight: 42 * 2,
          width: 670 * 2,
          zIndex: 999,
          fontWeight: 'bold',
        },
        {
          x: 40 * 2,
          y: 432 * 2,
          text: description,
          fontSize: 30 * 2,
          color: '#333',
          lineHeight: 48 * 2,
          lineNum: 5,
          width: 670 * 2,
          zIndex: 999,
        },
        {
          x: 194 * 2,
          y: 734 * 2,
          text: client?.shortName || client?.name,
          fontSize: 32 * 2,
          color: '#000',
          lineHeight: 40 * 2,
          lineNum: 8,
          width: 670 * 2,
          zIndex: 999,
        },
        {
          x: 194 * 2,
          y: 787 * 2,
          text: address,
          fontSize: 28 * 2,
          color: '#666',
          lineHeight: 40 * 2,
          lineNum: 1,
          width: 500 * 2,
          zIndex: 999,
        },
        {
          x: 40 * 2,
          y: 948 * 2,
          text: nickname,
          fontSize: 40 * 2,
          color: '#fff',
          lineHeight: 56 * 2,
          width: 670 * 2,
          zIndex: 999,
          fontWeight: 'bold',
        },
        {
          x: 40 * 2,
          y: 1014 * 2,
          text: '扫描二维码，查看职位详情',
          fontSize: 28 * 2,
          color: '#fff',
          lineHeight: 40 * 2,
          width: 670 * 2,
          zIndex: 999,
        },
      ];
      const imagesConfig = [
        {
          url: qrcodeUrl,
          width: 142 * 2,
          height: 142 * 2,
          x: 558 * 2,
          y: 929 * 2,
          zIndex: 10,
        },
        {
          url: 'https://oss.lingzhao.net/common/miniapp/icon-company.png',
          width: 86 * 2,
          height: 79 * 2,
          x: 57 * 2,
          y: 737 * 2,
          zIndex: 20,
        },
      ];
      const config1: any = {
        ...canvasConfig,
        blocks: [
          {
            x: 0,
            y: 0,
            width: 750 * 2,
            height: 1120 * 2,
            borderRadius: 40 * 2,
            backgroundColor: '#fff',
            // backgroundColor: "linear-gradient(to right, blue, pink)",
            zIndex: 0,
          },
          {
            x: 0,
            y: 880 * 2,
            width: 750 * 2,
            height: 240 * 2,
            // borderRadius: 40*2,
            backgroundColor: primaryColor,
            // backgroundColor: "linear-gradient(to right, blue, pink)",
            zIndex: 1,
          },
          ...blocksConfig,
        ],
        texts: textsConfig,
        images: imagesConfig,
      };
      const config2: any = {
        ...canvasConfig,
        blocks: [
          {
            x: 0,
            y: 0,
            width: 750 * 2,
            height: 1120 * 2,
            // borderRadius: 40 * 2,
            backgroundColor: primaryColor,
            // backgroundColor: "linear-gradient(to right, blue, pink)",
            zIndex: 0,
          },
          {
            x: 20 * 2,
            y: 20 * 2,
            width: 710 * 2,
            height: 860 * 2,
            borderRadius: 20 * 2,
            backgroundColor: '#fff',
            // backgroundColor: "linear-gradient(to right, blue, pink)",
            zIndex: 1,
          },
          ...blocksConfig,
        ],
        texts: textsConfig,
        images: imagesConfig,
      };
      advantages.forEach((item: any, index: number) => {
        if (index < 4) {
          const tagBlock = {
            x: (40 + 158 * index) * 2,
            y: 265 * 2,
            width: 142 * 2,
            height: 60 * 2,
            backgroundColor: '#F3F3F3',
            borderRadius: 8 * 2,
            zIndex: 3,
          };
          const tagText = {
            x: (40 + 158 * index) * 2,
            y: 280 * 2,
            width: 142 * 2,
            text: item.length > 4 ? item.slice(0, 4) + '...' : item,
            fontSize: 28 * 2,
            color: '#333',
            textAlign: 'center',
            lineHeight: 50 * 2,
            zIndex: 999,
          };
          config1.blocks.push(tagBlock);
          config2.blocks.push(tagBlock);
          config1.texts.push(tagText);
          config2.texts.push(tagText);
        }
      });
      setConfig([config1, config2]);
    }

    if (data && data.name && qrcodeUrl) {
      creatRssConfig().then();
    }
  }, [data, qrcodeUrl]);

  useEffect(() => {
    Taro.showLoading({
      title: `正在生成`,
    });
    if (config?.length && data) {
      setCanvasStatus(true);
    }
  }, [config, data]);

  // 绘制成功回调函数 （必须实现）=> 接收绘制结果、重置 TaroCanvasDrawer 状态
  const onCreateSuccess = (
    result: {
      tempFilePath: string;
      errMsg: string;
    },
    index: number,
  ): void => {
    const { tempFilePath, errMsg } = result;
    if (errMsg === 'canvasToTempFilePath:ok') {
      Taro.hideLoading();
      const newShareImages = [...shareImage];
      newShareImages[index] = tempFilePath;
      setShareImage(newShareImages);
      if (drawedCount < 1) {
        setDrawedCount(drawedCount + 1);
      }
    }
  };

  // 保存图片至本地
  const saveToAlbum = (): void => {
    wxapi
    .saveImageToPhotosAlbum({
      filePath: shareImage[swiperIndex],
    })
    .then((res: any) => {
      if (res.errMsg === 'saveImageToPhotosAlbum:ok') {
        return ui.showToast('保存图片成功');
      }
    })
    .catch(() => {
      wx.showModal({
        title: '提示',
        content: '需要您授权保存相册',
        showCancel: false,
        success() {
          wx.openSetting({
            success(settingData: any) {
              if (settingData.authSetting['scope.writePhotosAlbum']) {
                wx.showModal({
                  title: '提示',
                  content: '获取权限成功,再次保存图片即可成功',
                  showCancel: false,
                });
              } else {
                wx.showModal({
                  title: '提示',
                  content: '获取权限失败，无法保存到相册',
                  showCancel: false,
                });
              }
            },
            // complete(comp: any) {
            //   // console.log("complete", finishData)
            // },
          });
        },
      });
    });
  };

  return (
    <View className="share-container">
      <CustomNav />
      <View>
        {canvasStatus && (
          <Block>
            {drawedCount === 0 && !shareImage[0] && (
              <TaroCanvas
                key={1}
                config={config[0]} // 绘制配置
                onCreateSuccess={result => {
                  onCreateSuccess(result, 0);
                }} // 绘制成功回调
                // onCreateFail={onCreateFail} // 绘制失败回调
              />
            )}
            {drawedCount === 1 && !shareImage[1] && (
              <TaroCanvas
                key={2}
                config={config[1]} // 绘制配置
                onCreateSuccess={result => {
                  onCreateSuccess(result, 1);
                }} // 绘制成功回调
                // onCreateFail={onCreateFail} // 绘制失败回调
              />
            )}
          </Block>
        )}
        <View className="share-container__tip">
          长按图片，可直接「发送给好友或群」
        </View>
        <View className="share-container__imageWrap">
          <Swiper
            className="share-container__swiper"
            // snapToEdge={true}
            indicatorColor="#fff"
            indicatorActiveColor={primaryColor}
            vertical={false}
            circular={false}
            indicatorDots={!!shareImage?.length}
            autoplay={false}
            // previousMargin={"30px"}
            // nextMargin={"30px"}
            onChange={value => {
              setSwiperIndex(value.detail.current);
            }}
          >
            <SwiperItem key="1">
              <View className="share-container__swiper-item">
                <Image
                  className="share-container__image"
                  src={shareImage[0]}
                  mode="heightFix"
                  lazy-load
                  show-menu-by-longpress
                />
              </View>
            </SwiperItem>
            <SwiperItem key="2">
              <View className="share-container__swiper-item">
                <Image
                  className="share-container__image"
                  src={shareImage[1]}
                  mode="heightFix"
                  lazy-load
                  show-menu-by-longpress
                />
              </View>
            </SwiperItem>
          </Swiper>
        </View>
        <Footer>
          <Button
            className="share-container__save"
            full
            type="secondary"
            circle
            onClick={saveToAlbum}
          >
            保存图片到相册
          </Button>
        </Footer>
      </View>
    </View>
  );
};
export default ShareContainer;
