@import '@styles/variables/default.scss';
@import '@styles/mixins/index.scss';


.share-container{
    height: 100vh;
    background-color: #f3f3f3;
    &__swiper{
        width: 100%;
        height: 65vh;
        &-item{
            margin:0 20px;
            height: 100%;
        }
    }
    &__imageWrap{
        height: calc(100vh - 220px);
        display: flex;
        align-items: center;
        justify-content: center;
    }
    &__tip{
        display: flex;
        justify-content: center;
        align-items: flex-end;
        color: #666;
        font-size: 16px;
        height: 120px;
    }
    &__loading{
        display: flex;
        align-items: center;
        margin: 0 auto;
        overflow: hidden;
        border-radius: 10px;
        height: 60vh;
        width: 100%;
        background-color: #fff;
    }
    &__image,&__loading{

        display: flex;
        align-items: flex-end;
        // height: 300px;
        // background-color: red;
        margin: 0 auto;
        overflow: hidden;
        border-radius: 10px;
        height: 60vh;
        // border: solid 1px #ddd;
    }
    &__btns{
        height: 100px;
        gap: 8px;
        padding: 0 28px;
        display: flex;
        justify-content: space-between;
    }
    &__tips{
        padding:16px;
        font-size: 14px;
        color: #666;
    }
    .canvas-element {
        position: fixed;
        top: -1000px;
        left: -1000px;
      
        &__nickName {
          font-size: 32px;
          font-weight: bold;
        }
        &__share {
          font-size: 28px;
          font-weight: 300;
        }
      }
}