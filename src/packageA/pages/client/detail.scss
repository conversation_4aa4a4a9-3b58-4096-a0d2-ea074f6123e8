@import '@styles/variables/default.scss';
@import '@styles/mixins/index.scss';

.client-detail{
  .position-title{
    padding-left: 15px;
    color: $color-text;
    height: 32px;
    line-height: 32px;
    border-bottom: solid 0.5px #eee;
  }
  height: 100vh;
  top:0;
  width: 100%;
  position: fixed;
  overflow: hidden;
  &.dark{
    background-image: url(https://candidate.lingzhao.net/candidate-miinprogram/client_bg.png);
    background-repeat: no-repeat;
    background-size: cover;
    color:#fff;
    .qz-Panel{
      background-color: transparent;
    }
    .qz-clientTags__item{
      background-color: #555;
      color:#fff;
      border:none;
      font-size: 12px;
    }
    .qz-clientBaseInfo__name,
    .qz-Panel__title,
    .qz-Panel__content{
      color:#fff;
    }
    .qz-clientBaseInfo__baseInfo{
      color:#aaa;
    }
    .qz-clientBaseInfo__logoWrap{
      border:none;
    }
    .qz-button--small{
      border: solid 1px #fff !important;
      color: #fff !important;
      background-color: transparent !important;
    }
  }
  
}
.client-detail-scrollview{
  width: 100%;
  height: 100vh;
}