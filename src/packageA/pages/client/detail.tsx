import { ScrollView, View } from "@tarojs/components";
import { FC, useState, useEffect } from "react";
import { container } from "@landrover/business/index";
import Taro, { getCurrentInstance } from "@tarojs/taro";
import CustomNav from "@components/CustomNav";
import ClientBaseInfoCard from "./components/ClientBaseInfoCard";
import PositionCard from "@components/PositionCard";
import FloatLayout from "@components/FloatLayout";
import ClientIntro from "./components/ClientIntro";
import ClientAddress from "./components/ClientAddress";
import ScrollGallery from "@components/ScrollGallery";
import "./detail.scss";

type ClientDetailProps = {};
const ClientDetail: FC<ClientDetailProps> = () => {
  const { MENU_BUTTON, SCREEN_WIDTH } = Taro.getApp().$app;
  const { right, bottom } = MENU_BUTTON;
  const NAV_GAP = SCREEN_WIDTH - right;
  const navbarHeight = bottom + NAV_GAP;
  const { router } = getCurrentInstance();
  const id = router?.params?.id;
  const positionId = router?.params?.positionId;
  const [clientData, setClientData] = useState<ClientTypes>();

  useEffect(() => {
    container.saasService.getClientDetail(Number(id)).then(res => {
      setClientData(res);
    });
  }, []);

  const [positionData, setPositionData] = useState<PositionTypes[]>([]);
  useEffect(() => {
    container.saasService
      .getPositions({
        clientId: id,
        pageSize: 100
      })
      .then(res => {
        setPositionData(res.list);
      });
  }, []);

  const [sourcePosition,setSourcePosition] = useState<PositionTypes>();
  useEffect(() => {
    if(positionId && positionData){
      positionData.forEach((item)=>{
        if(item.id === Number(positionId)){
          setSourcePosition(item)
        }
      })
    }
  }, [positionId,positionData]);
  

  const [disableScroll, setDisableScroll] = useState<boolean>(false);
  const [scrollTop, setScrollTop] = useState(0);
  const [visible, setVisible] = useState<boolean>(true);
  const handleScroll = e => {
    setVisible(e.detail.scrollTop > scrollTop);
    setScrollTop(e.detail.scrollTop);
  };

  const onChangeListData = (id: number) => {
    const data = [...positionData];
    data.forEach(item => {
      if (item.id === id) {
        item.flow = {
          id
        };
      }
    });
    setPositionData(data);
  };

  if (!clientData) return null;

  return (
    <View className="container client-detail dark">
      <ScrollView
        scrollY={!disableScroll}
        scrollWithAnimation
        className="client-detail-scrollview"
        onScroll={handleScroll}
      >
        <View
          style={{
            paddingTop: navbarHeight,
            paddingBottom: "160px"
          }}
        >
          <CustomNav dynamicOpacity={false} />
          <ClientBaseInfoCard data={clientData} sourcePosition={sourcePosition} />
          <ClientIntro data={clientData} />
          <ClientAddress data={clientData} />
          <ScrollGallery data={clientData.galleries} title="公司环境" />
        </View>
      </ScrollView>

      <FloatLayout
        visible={visible}
        onChangeDisableScroll={value => {
          setDisableScroll(value);
        }}
      >
        <View>
          <View className="position-title">
            在招职位({positionData?.length})
          </View>
          {positionData.map(item => {
            return (
              <PositionCard
                positionData={item}
                key={item.id}
                onChange={id => {
                  onChangeListData && onChangeListData(id);
                }}
              />
            );
          })}
        </View>
      </FloatLayout>
    </View>
  );
};
export default ClientDetail;
