import QzButton from "@components/Button";
import IconFont from "@components/iconfont";
import Panel from "@components/Panel";
import { View } from "@tarojs/components";
import { FC, useEffect, useState } from "react";
import "./index.scss";

type ClientAddressProps = {
  data: ClientTypes;
};

const ClientAddress: FC<ClientAddressProps> = ({ data }) => {
  const { address, addressCoord } = data;
  const [location, setLocation] = useState<any>([]);

  useEffect(() => {
    if (addressCoord) {
      setLocation([
        Number(addressCoord.split(",")[0]),
        Number(addressCoord.split(",")[1])
      ]);
    }
  }, []);
  const getAddressCoordLocation = () => {
    if (!location[0] || !location[1]) return;
    wx.getLocation({
      type: "wgs84",
      success: () => {
        wx.openLocation({
          //​使用微信内置地图查看位置。
          latitude: location[0], //要去的纬度-地址
          longitude: location[1], //要去的经度-地址
          name: address,
          address
        });
      }
    });
  };

  if (!address) return null;

  return (
    <View className="qz-clientAddress">
      <Panel title="公司地址">
        <View className="qz-clientAddress__content">
          <View
            style={{
              flex: 1
            }}
            onClick={() => {
              getAddressCoordLocation();
            }}
          >
            {address}
          </View>
          {location[0] && location[1] && (
            <QzButton
              customStyle={{ width: "80px" }}
              onClick={() => {
                getAddressCoordLocation();
              }}
              size="small"
              type="secondary"
              circle
            >
              <View className="qz-clientAddress__btn">
                <View className="qz-clientAddress__btn-icon">
                  <IconFont size={14} name="mp-daohang" color="#fff" />
                </View>
                导航
              </View>
            </QzButton>
          )}
        </View>
      </Panel>
    </View>
  );
};
export default ClientAddress;
