import Panel from "@components/Panel";
import { View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { FC, useEffect, useState } from "react";
import "./index.scss";

type ClientIntroProps = {
  data: ClientTypes;
};
const ClientIntro: FC<ClientIntroProps> = ({ data }) => {
  const [introMore, setIntroMore] = useState(false);
  const { intro } = data;

  useEffect(() => {
    if (intro) {
      Taro.nextTick(() => {
        const query = Taro.createSelectorQuery();
        query.select(`#intro`).boundingClientRect();
        query.exec(function(res) {
          setIntroMore(res[0].height > 66);
        });
      });
    }
  }, []);
  if(!intro) return null;
  return (
    <View className="qz-clientIntro">
      <Panel title="公司介绍">
        <View id="intro" className={introMore ? "qz-clientIntro__content" : ""}>
          {intro}
        </View>
        {introMore && (
          <View
            className="qz-clientIntro__btn-more"
            onClick={() => {
              setIntroMore(false);
            }}
          >
            查看更多
          </View>
        )}
      </Panel>
    </View>
  );
};
export default ClientIntro;
