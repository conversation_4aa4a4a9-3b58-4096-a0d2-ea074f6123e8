import Panel from '@components/Panel'
import { View } from '@tarojs/components'
import type { FC } from 'react'
import ClientBaseInfo from '../ClientBaseInfo'
import ClientTags from '../ClientTags'

type ClientBaseInfoCardProps = {
  data: ClientTypes
  sourcePosition?: PositionTypes
}
const ClientBaseInfoCard: FC<ClientBaseInfoCardProps> = ({data,sourcePosition}) => {
  return (
    <Panel style={{
      paddingLeft: 0,
      paddingRight:0
    }}>
      <View style={{
        display:'flex',
        flexDirection:"column",
        gap: '16px'
      }}>
        <ClientBaseInfo data={data} />
        <ClientTags data={data} sourcePosition={sourcePosition} />
      </View>
    </Panel>
  )
}
export default ClientBaseInfoCard