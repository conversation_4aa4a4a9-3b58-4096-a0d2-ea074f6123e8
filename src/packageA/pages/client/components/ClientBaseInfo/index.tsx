import { Image, View } from '@tarojs/components'
import type { FC } from 'react'
import { useSelector } from 'react-redux'
import './index.scss'

type ClientBaseInfoProps = {
  data: ClientTypes
}
const ClientBaseInfo: FC<ClientBaseInfoProps> = ({data}) => {
  const {dictMap={}} = useSelector((state:any) => state.dict);
  const scaleMap = dictMap?.client?.scale;
  const industryMap = dictMap?.common?.industry;
  const {name,id,logo, scale=8, industry=[1004, 1004001]} = data;
  const getClientBaseInfo = ()=>{
    const info = [];
    if(scaleMap[scale]){
      info.push(scaleMap[scale])
    }
    if(industryMap[industry[0]]){
      info.push(industryMap[industry[0]].name)
    }
    return  info.join(' · ')
  }
  return (
    <View className="qz-clientBaseInfo__content">
     
     <View className="qz-clientBaseInfo__main">
       <View className="qz-clientBaseInfo__name">{name}</View>
       <View className="qz-clientBaseInfo__baseInfo">{getClientBaseInfo()}</View>
     </View>
     <View className="qz-clientBaseInfo__logoWrap">
        {!!logo?.url && <Image className="qz-clientBaseInfo__logo" src={logo.url} mode="aspectFill" />}
     </View>
   </View>
  )
}
export default ClientBaseInfo