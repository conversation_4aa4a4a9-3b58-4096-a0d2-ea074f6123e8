
@import '@styles/variables/default.scss';
@import '@styles/mixins/index.scss';

.qz-clientBaseInfo{
  &__content{
    display: flex;
    justify-content: center;
    padding: 0 15px;
  }
  &__logoWrap{
    line-height: 0;
    border: solid 1px #eee;
    border-radius: 2px;
  }
  &__logo{
    width:48px;
    height: 48px;
  }
  &__main{
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: space-between;
    gap: 8px
  }
  &__name{
    font-size: 20px;
    line-height: 24px;
    font-weight: 500;
    color: $color-text;
  }
  &__baseInfo{
    line-height: 16px;
  }
  
}
