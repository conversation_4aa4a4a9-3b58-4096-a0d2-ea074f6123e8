import { ScrollView, View } from '@tarojs/components';
import type { FC } from 'react'
import './index.scss'

type ClientTagsProps = {
  data: ClientTypes
  sourcePosition?: PositionTypes
}
const ClientTags: FC<ClientTagsProps> = ({data,sourcePosition}) => {
  let newAdvantages:any;
  if(data?.advantages?.length){
    newAdvantages  =  data.advantages
  }else{
    if(sourcePosition?.advantages?.length){
      newAdvantages  =  sourcePosition.advantages
    }else{
      newAdvantages = [];
    }
  }
  if(!newAdvantages?.length)  return null;
 
  return (
    <View className="qz-clientTags">
      <ScrollView
        scrollX
        scrollWithAnimation
        className="qz-clientTags__scrollView"
      >
        <View className="qz-clientTags__content">
        {newAdvantages?.map((item)=>{
          return <View className="qz-clientTags__item"  key={item}>{item}</View>
        })}
        </View>
      </ScrollView>
    </View>
  )
}
export default ClientTags