import Desc from '@components/Desc';
import JumpTitle from '@components/JumpTitle';
import ResumeEditCard from '@components/ResumeEditCard';
import { Block, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { formatRangePicker } from '@utils/format';
import React from 'react';
import './index.scss';

type ProjectCardProps = {
  resume: ResumeTypes;
};
const ProjectCard: React.FC<ProjectCardProps> = ({ resume }) => {
  const { projects } = resume;
  const getTimeRange = (item: any) => {
    const { startTime, endTime, soFar } = item;
    return formatRangePicker(startTime, endTime, soFar);
  };

  return (
    <View className="qz-ProjectCard">
      <ResumeEditCard
        title="项目经历"
        onEdit={() => {
          Taro.navigateTo({
            url: `/packageA/pages/resume/edit?type=project`,
          });
        }}
      >
        <View className="qz-ProjectCard__content">
          {projects.length ? (
            <Block>
              {projects.map(item => {
                const {
                  name,
                  description,
                  responsibilities,
                  achievements,
                  company,
                  position,
                } = item;
                const timeRange = getTimeRange(item);
                return (
                  <View key={item.id} className="qz-ProjectCard__content-item">
                    <JumpTitle
                      onClick={() => {
                        Taro.navigateTo({
                          url: `/packageA/pages/resume/edit?type=project&id=${item.id}`,
                        });
                      }}
                      title={<View>{name}</View>}
                    />
                    {(timeRange || company || position) && <Desc>
                      {timeRange && <Text>{getTimeRange(item)}</Text>}
                      {company && <Text>{company}</Text>}
                      {position && <Text>{position}</Text>}
                    </Desc>}
                    {description && (
                      <Desc direction="column" style={{ gap: '0px' }}>
                        <Text>项目描述:</Text>
                        <Text>{description}</Text>
                      </Desc>
                    )}
                    {responsibilities && (
                      <Desc direction="column" style={{ gap: '0px' }}>
                        <Text>项目职责:</Text>
                        <Text>{responsibilities}</Text>
                      </Desc>
                    )}
                    {achievements && (
                      <Desc direction="column" style={{ gap: '0px' }}>
                        <Text>项目业绩:</Text>
                        <Text>{achievements}</Text>
                      </Desc>
                    )}
                  </View>
                );
              })}
            </Block>
          ) : (
            <Desc>暂无项目经历，请添加项目经历</Desc>
          )}
        </View>
      </ResumeEditCard>
    </View>
  );
};

export default ProjectCard;
