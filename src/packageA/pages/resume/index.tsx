import { FC, useState } from 'react';
import { useDidShow } from '@tarojs/taro';
import { container } from '@landrover/business/index';
import { View } from '@tarojs/components';
import { useDispatch } from 'react-redux';
import { setResumeData as setStoreResumeData } from '@stores/actions/resume';
import { decryptSensitiveData } from '@utils/index';
import BasicCard from './BasicCard';
import SeekPurposeCard from './SeekPurposeCard';
import EducationCard from './EducationCard';
import WorkCard from './WorkCard';
import ProjectCard from './ProjectCard';
import SelfRemarkCard from './SelfRemarkCard';
import './index.scss';

type ResumeDetailProps = {}
const ResumeDetail: FC<ResumeDetailProps> = () => {
  const dispatch = useDispatch();
  const [resumeData, setResumeData] = useState<ResumeTypes>();

  useDidShow(() => {
    const { auth } = container.userService;
    container.saasService.getResumeDetail().then((res: ResumeTypes) => {
      const resume = decryptSensitiveData(res, auth);
      dispatch(setStoreResumeData(resume));
      setResumeData(resume);
    });
  });

  return resumeData ? (
    <View className="qz-ResumeDetail">
      <BasicCard resume={resumeData} />
      <SeekPurposeCard resume={resumeData} />
      <EducationCard resume={resumeData} />
      <WorkCard resume={resumeData} />
      <ProjectCard resume={resumeData} />
      <SelfRemarkCard resume={resumeData} />
    </View>
  ) : null;
};
export default ResumeDetail;
