@import '@styles/variables/default.scss';

.container-complete{
  height: 100vh;
  background-color: $page-bg;
  padding: 0 20px;
  .form-resume-complete{
    display: block;
    margin-top: 36px;
  }
  .form-cell{
    margin-bottom: 24px;
    &:last-of-type{
      margin-bottom: 48px;
    }
    &:last-child{
      margin-bottom: 48px;
    }
    &__label{
      margin-bottom: 8px;
    }
    &__field{
      &-gender{
        display: flex;
        gap: 24px;
        &-item{
          position: relative;
        }
        &-icon{
          width: 16px;
          height: 16px;
          position: absolute;
          bottom: 4px;
          left: 50%;
          margin-left: -8px;
          background-color: #fff;
          border-radius: 16px;
          display: none;
          &.selected{
            display: inherit;
          }
        }
      }
      &-name{
        padding: 0 20px;
        height: 40px;
        border: solid 1px #ddd;
        border-radius: 40px;
      }
    }
  }
}