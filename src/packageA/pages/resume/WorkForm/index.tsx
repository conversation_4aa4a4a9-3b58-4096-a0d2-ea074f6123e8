import type { FC } from 'react';
import { useState } from 'react';
import Taro from '@tarojs/taro';
import { View, Input, Block, Form, Textarea } from '@tarojs/components';
import { useDispatch, useSelector } from 'react-redux';
import { setResumeData as setStoreResumeData } from '@stores/actions/resume';
import { container, ui } from '@landrover/business/index';
import FormItem from '@components/FormItem';
import MonthPicker from '@components/MonthPicker';
import dayjs from 'dayjs';
import ArrowRightIcon from '@components/ArrowRightIcon';
import SectionFooter from '../SectionFooter';

type WorkFormProps = {
  id: number
}
const fieldMap = {
  'company': '公司名称',
  'position': '职位名称',
  'startTime': '开始时间',
  'endTime': '结束时间',
  'description': '工作内容',
  'achievements': '工作业绩',
};
const WorkForm: FC<WorkFormProps> = ({ id }) => {
  const { dictArr = {} } = useSelector((state: any) => state.dict);
  const requiredFields = dictArr?.form?.resume?.required?.works || [];
  const isEdit = !!id;
  const dispatch = useDispatch();
  const storeResumeData = useSelector((state: any) => state.resume);
  let currentWorkData: any = {};
  if (isEdit) {
    storeResumeData.works.forEach((item: any) => {
      if (item.id === Number(id)) currentWorkData = item;
    });
  }

  const [workData, setWorkData] = useState<WorkTypes>(currentWorkData);

  const handleDelete = () => {
    container.saasService.deleteResumeSection('works', id).then(() => {
      const newWorks = storeResumeData.works.filter((item: any) => item.id !== Number(id));
      dispatch(setStoreResumeData({ ...storeResumeData, works: newWorks }));
      Taro.navigateBack();
    });
  };

  const handleSubmit = () => {
    Taro.showLoading();

    // 过滤所有字符串类型的字段，去除前后空格
    const formattedData = {};
    for (const [key, value] of Object.entries(workData)) {
      formattedData[key] = value;
      if (typeof value === 'string' && value !== value.trim()) {
        formattedData[key] = value.trim();
        setWorkData({ ...workData, [key]: value.trim() });
      }
    }

    // 校验必填字段是否为空
    for (const key of requiredFields) {
      if (!formattedData[key]) {
        return ui.showToast(`${fieldMap[key]}不能为空`);
      }
    }

    const workPromise = container.saasService[isEdit ? 'updateResumeSection' : 'addResumeSection']('works', workData);

    workPromise.then((result: ResumeTypes) => {
      if (result) {
        let newWorks: WorkTypes[];
        if (isEdit) {
          newWorks = [];
          storeResumeData.works.forEach((item: WorkTypes) => {
            if (item.id === workData?.id) {
              newWorks.push(result);
            } else {
              newWorks.push(item);
            }
          });
        } else {
          newWorks = [result, ...storeResumeData.works];
        }

        dispatch(setStoreResumeData({ ...storeResumeData, works: newWorks }));
        Taro.navigateBack();
      }
    })
    .catch((res: any) => {
      return ui.showToast(res.errorMessage);
    });
  };

  const { company, position, startTime, endTime, description, achievements, soFar } = workData || {};

  return (
    <View className="qz-WorkForm">
      <Form onSubmit={handleSubmit}>
        <FormItem label="公司" required={requiredFields.includes('name')}>
          <Input
            name="company"
            className="qz-FormItem__value"
            type="text"
            value={company}
            onInput={(e) => setWorkData({ ...workData, company: e.detail.value })}
            placeholder="请输入公司名称"
            placeholderClass="qz-FormItem__placeholder"
          />
        </FormItem>
        <FormItem label="职位" required={requiredFields.includes('position')}>
          <Input
            name="position"
            className="qz-FormItem__value"
            type="text"
            value={position}
            onInput={(e) => setWorkData({ ...workData, position: e.detail.value })}
            placeholder="请输入职位名称"
            placeholderClass="qz-FormItem__placeholder"
          />
        </FormItem>
        <FormItem label="开始时间" required={requiredFields.includes('startTime')}>
          <Block>
            <MonthPicker
              name="startTime"
              value={startTime ? dayjs(startTime).format('YYYY年M月') : ''}
              onChange={(values: any) => setWorkData({ ...workData, ...values })}
            />
            <ArrowRightIcon />
          </Block>
        </FormItem>
        <FormItem label="结束时间" required={requiredFields.includes('endTime')}>
          <Block>
            <MonthPicker
              name="endTime"
              soFar={soFar || 0}
              value={endTime ? dayjs(endTime).format('YYYY年M月') : ''}
              onChange={(values) => setWorkData({ ...workData, ...values })}
            />
            <ArrowRightIcon />
          </Block>
        </FormItem>
        <FormItem label="工作内容" required={requiredFields.includes('description')}>
          <Textarea
            placeholder="请输入工作内容"
            value={description}
            className="qz-FormItem__value"
            placeholderClass="qz-FormItem__placeholder"
            onInput={(e) =>
              setWorkData({ ...workData, description: e.detail.value })
            }
          ></Textarea>
        </FormItem>
        <FormItem label="工作业绩" required={requiredFields.includes('achievements')}>
          <Textarea
            placeholder="请输入工作业绩"
            value={achievements}
            className="qz-FormItem__value"
            placeholderClass="qz-FormItem__placeholder"
            onInput={(e) =>
              setWorkData({ ...workData, achievements: e.detail.value })
            }
          ></Textarea>
        </FormItem>
        <SectionFooter name="工作经历" isEdit={isEdit} onDelete={handleDelete} />
      </Form>
    </View>
  );
};
export default WorkForm;
