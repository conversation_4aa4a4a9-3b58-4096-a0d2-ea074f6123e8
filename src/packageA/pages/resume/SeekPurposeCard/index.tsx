import Desc from '@components/Desc';
import JumpTitle from '@components/JumpTitle';
import ResumeEditCard from '@components/ResumeEditCard';
import { Block, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useSelector } from 'react-redux';
import React from 'react';
import './index.scss';

type SeekPurposeCardProps = {
  resume: ResumeTypes;
};
const SeekPurposeCard: React.FC<SeekPurposeCardProps> = ({ resume }) => {
  const { dictMap = {} } = useSelector((state: any) => state.dict);
  const { currentStatus, expectPosition, expectSalary } = resume;
  const isCreate = !expectPosition && !currentStatus && !expectSalary;
  return (
    <ResumeEditCard title="求职意向" type="edit" onEdit={() => Taro.navigateTo({ url: `/packageA/pages/resume/edit?type=seekPurpose` })}>
      <View>
        {!isCreate && <JumpTitle
          iconShow={false}
          title={
            <Block>
              <View>{expectPosition}</View>
              <View>{expectSalary ? `${expectSalary}${expectSalary.indexOf('面议') > -1 ? '' : '元'}` : ''}</View>
            </Block>
          }
        />}
        {currentStatus ? <Desc>求职状态：{dictMap?.resume?.currentStatus[currentStatus]}</Desc> : <Desc>填写求职意向，提升求职的匹配度</Desc>}
      </View>
    </ResumeEditCard>
  );
};

export default SeekPurposeCard;
