import { View } from '@tarojs/components';
import ResumeRequirements from '@components/ResumeRequirements';
import ResumeAvatar from '@components/ResumeAvatar';
import { FC } from 'react';
import ResumeNameEdit from '@components/ResumeNameEdit';
import './index.scss';

type Props = {
  resume: ResumeTypes;
};

/**
 * 薪资展示.
 * @param {array} tags
 */
const ResumeBasicCard: FC<Props> = ({ resume }: any) => {
  const { avatar, gender } = resume;

  return (
    <View className="qz-ResumeBasicCard panel">
      <View className="qz-ResumeBasicCard__content">
        <ResumeNameEdit resume={resume} />
        <ResumeRequirements resume={resume} />
      </View>
      <ResumeAvatar size={64} url={avatar?.url} gender={gender} />
    </View>
  );
};

export default ResumeBasicCard;
