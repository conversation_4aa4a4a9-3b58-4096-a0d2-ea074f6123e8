import type { FC } from 'react';
import { useState } from 'react';
import Taro from '@tarojs/taro';
import { View, Input, Block, Form } from '@tarojs/components';
import NativePicker from '@components/NativePicker';
import { useDispatch, useSelector } from 'react-redux';
import { setResumeData as setStoreResumeData } from '@stores/actions/resume';
import { container, ui } from '@landrover/business/index';
import FormItem from '@components/FormItem';
import MonthPicker from '@components/MonthPicker';
import dayjs from 'dayjs';
import ArrowRightIcon from '@components/ArrowRightIcon';
import SectionFooter from '../SectionFooter';

type EducationFormProps = {
  id: number
}
const fieldMap = {
  'school': '学校',
  'degree': '学历',
  'startTime': '开始时间',
  'endTime': '结束时间',
  'discipline': '专业',
};
const EducationForm: FC<EducationFormProps> = ({ id }) => {
  const { dictArr = {} } = useSelector((state: any) => state.dict);
  const degreeOptions = dictArr?.common?.degree;
  const requiredFields = dictArr?.form?.resume?.required?.educations || [];
  const isEdit = !!id;
  const dispatch = useDispatch();
  const storeResumeData = useSelector((state: any) => state.resume);
  let currentEducationData: any = {};
  if (isEdit) {
    storeResumeData.educations.forEach((item: any) => {
      if (item.id === Number(id)) currentEducationData = item;
    });
  }

  const [educationData, setEducationData] = useState<EducationTypes>(currentEducationData);

  const handleDelete = () => {
    container.saasService.deleteResumeSection('educations', id).then(() => {
      const newEducations = storeResumeData.educations.filter((item: any) => {
        return item.id !== Number(id);
      });
      dispatch(setStoreResumeData({ ...storeResumeData, educations: newEducations }));
      Taro.navigateBack();
    });
  };

  const handleSubmit = () => {
    Taro.showLoading();

    // 过滤所有字符串类型的字段，去除前后空格
    const formattedData = {};
    for (const [key, value] of Object.entries(educationData)) {
      formattedData[key] = value;
      if (typeof value === 'string' && value !== value.trim()) {
        formattedData[key] = value.trim();
        setEducationData({ ...educationData, [key]: value.trim() });
      }
    }

    // 校验必填字段是否为空
    for (const key of requiredFields) {
      if (!formattedData[key]) {
        return ui.showToast(`${fieldMap[key]}不能为空`);
      }
    }

    const educationPromise = container.saasService[isEdit ? 'updateResumeSection' : 'addResumeSection']('educations', educationData);

    educationPromise.then((result: ResumeTypes) => {
      if (result) {
        let newEducations: EducationTypes[];
        if (isEdit) {
          newEducations = [];
          storeResumeData.educations.forEach((item: EducationTypes) => {
            if (item.id === educationData?.id) {
              newEducations.push(result);
            } else {
              newEducations.push(item);
            }
          });
        } else {
          newEducations = [result, ...storeResumeData.educations];
        }

        dispatch(setStoreResumeData({ ...storeResumeData, educations: newEducations }));
        Taro.navigateBack();
      }
    })
    .catch((res: any) => {
      return ui.showToast(res.errorMessage);
    });
  };

  const { school, degree, startTime, endTime, discipline, soFar } = educationData || {};

  return (
    <View className="qz-EducationForm">
      <Form onSubmit={handleSubmit}>
        <FormItem label="学校" required={requiredFields.includes('name')}>
          <Input
            name="school"
            className="qz-FormItem__value"
            type="text"
            value={school}
            onInput={(e) => {
              setEducationData({ ...educationData, school: e.detail.value });
            }}
            placeholder="请输入学校"
            placeholderClass="qz-FormItem__placeholder"
          />
        </FormItem>
        <FormItem label="学历" required={requiredFields.includes('degree')}>
          <Block>
            <NativePicker
              range={degreeOptions}
              value={degree}
              name="degree"
              mode="selector"
              onChange={(val) =>
                setEducationData({ ...educationData, degree: val.id })
              }
              placeholder="请选择学历"
            />
            <ArrowRightIcon />
          </Block>
        </FormItem>

        <FormItem label="开始时间" required={requiredFields.includes('startTime')}>
          <Block>
            <MonthPicker
              name="startTime"
              value={startTime ? dayjs(startTime).format('YYYY年M月') : ''}
              onChange={(values: any) => setEducationData({ ...educationData, ...values })}
            />
            <ArrowRightIcon />
          </Block>
        </FormItem>
        <FormItem label="结束时间" required={requiredFields.includes('endTime')}>
          <Block>
            <MonthPicker
              name="endTime"
              soFar={soFar || 0}
              timeRange={[-4, 20]}
              value={endTime ? dayjs(endTime).format('YYYY年M月') : ''}
              onChange={(values: any) => setEducationData({ ...educationData, ...values })}
            />
            <ArrowRightIcon />
          </Block>
        </FormItem>
        <FormItem label="专业" required={requiredFields.includes('discipline')}>
          <Input
            name="discipline"
            className="qz-FormItem__value"
            type="text"
            value={discipline}
            onInput={(e) => setEducationData({ ...educationData, discipline: e.detail.value })}
            placeholder="请输入专业"
            placeholderClass="qz-FormItem__placeholder"
          />
        </FormItem>
        <SectionFooter name="教育经历" isEdit={isEdit} onDelete={handleDelete} />
      </Form>
    </View>
  );
};
export default EducationForm;
