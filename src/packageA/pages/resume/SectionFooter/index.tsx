import QzButton from '@components/Button';
import Footer from '@components/Footer';
import { Block } from '@tarojs/components';
import Taro from '@tarojs/taro';
import type { FC } from 'react';
import './index.scss';

type SectionFooterProps = {
  name: string
  isEdit: boolean
  onDelete: () => void
}
const SectionFooter: FC<SectionFooterProps> = ({ name, isEdit, onDelete }) => {
  return (
    <Footer>
      <Block>
        {isEdit && <QzButton
          full
          className="qz-SectionFooter__btn-delete"
          circle
          type="gray"
          onClick={() => {
            Taro.showModal({
              title: '提示',
              content: `确定删除该条${name}吗？`,
              success: function (res) {
                if (res.confirm) {
                  onDelete();
                }
              },
            });
          }}
        >
          删除
        </QzButton>}
        <QzButton full formType="submit" circle type={isEdit ? 'primary' : 'secondary'}>
          保存
        </QzButton>
      </Block>
    </Footer>
  );
};
export default SectionFooter;
