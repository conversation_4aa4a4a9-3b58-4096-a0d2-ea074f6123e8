import type { FC } from 'react';
import { useState } from 'react';
import Taro from '@tarojs/taro';
import { View, Input, Block, Form, Text } from '@tarojs/components';
import NativePicker from '@components/NativePicker';
import QzButton from '@components/Button';
import { useDispatch, useSelector } from 'react-redux';
import { setResumeData as setStoreResumeData } from '@stores/actions/resume';
import { container, ui } from '@landrover/business/index';
import Footer from '@components/Footer';
import FormItem from '@components/FormItem';
import ArrowRightIcon from '@components/ArrowRightIcon';

type SeekPurposeFormProps = {}
const SeekPurposeForm: FC<SeekPurposeFormProps> = () => {
  const dispatch = useDispatch();
  const storeResumeData = useSelector((state: any) => state.resume);
  const [resumeData, setResumeData] = useState<ResumeTypes>(storeResumeData);
  const { currentStatus } = resumeData;
  const { dictArr = {} } = useSelector((state: any) => state.dict);
  const statusOptions = dictArr.resume.currentStatus;
  const handleSubmit = () => {
    Taro.showLoading();

    container.saasService.editBaseResume(resumeData)
    .then((result: ResumeTypes) => {
      if (result) {
        dispatch(setStoreResumeData({ ...storeResumeData, ...result }));
        Taro.navigateBack();
      }
    })
    .catch((res: any) => {
      return ui.showToast(res.errorMessage);
    });
  };
  return (
    <View className="qz-SeekPurposeForm">
      <Form onSubmit={handleSubmit}>
        <FormItem label="求职状态">
          <Block>
            <NativePicker
              range={statusOptions}
              value={currentStatus}
              name="currentStatus"
              mode="selector"
              onChange={(val): void =>
                setResumeData({ ...resumeData, currentStatus: val.id })
              }
              placeholder="请选择求职状态"
            />
            <ArrowRightIcon />
          </Block>
        </FormItem>
        <FormItem label="期望职位">
          <Input
            name="expectPosition"
            className="qz-FormItem__value"
            type="text"
            value={resumeData.expectPosition}
            onInput={(e) => setResumeData({ ...resumeData, expectPosition: e.detail.value })}
            placeholder="请输入期望职位"
            placeholderClass="qz-FormItem__placeholder"
          />
        </FormItem>
        <FormItem label="期望薪资">
          <Block>
            <Input
              className="qz-FormItem__value"
              type="text"
              name="expectSalary"
              value={resumeData.expectSalary}
              onInput={(e) => setResumeData({ ...resumeData, expectSalary: e.detail.value })}
              placeholder="请输入期望薪资（单位：元）"
              placeholderClass="qz-FormItem__placeholder"
            />
            <Text>元</Text>
          </Block>
        </FormItem>
        <Footer className="position-details__footer">
          <QzButton full formType="submit" className="position-details__submit" circle type="secondary">
            保存
          </QzButton>
        </Footer>
      </Form>
    </View>
  );
};
export default SeekPurposeForm;
