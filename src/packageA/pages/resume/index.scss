@import '@styles/variables/default.scss';
@import '@styles/mixins/index.scss';
.candidate-details {
  background-color: #fff;
  .card,
  .panel {
    margin: 0 auto;
    width: 345px;
    padding: 20px 0;
    @include hairline-bottom($color-border-shallow);
  }
  .panel {
    &-title,
    &-position {
      font-size: 18px;
      font-weight: 500;
      color: #2B2B2B;
      line-height: 22px;
    }
    &-position {
      margin-top: 15px;
      font-size: 16px;
    }

    &-desc {
      margin-top: 10px;
      font-size: 14px;
      line-height: 14px;
      color: #ABABAB;
      &.display-flex {
        @include display-flex();
        @include align-items(center);
        @include justify-content(space-between);
      }
      &__status {
        font-size: 14px;
        color: $primary-color;
      }
    }

    &:last-child::after {
      display: none;
    }

    &-cell {
      @include hairline-bottom($color-border-shallow);
      padding-bottom: 20px;
      margin-bottom: 20px;

      &:last-child::after {
        display: none;
      }
    }
  }
}


