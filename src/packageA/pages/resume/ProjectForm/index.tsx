import type { FC } from 'react';
import { useState } from 'react';
import Taro from '@tarojs/taro';
import { View, Input, Block, Form, Textarea } from '@tarojs/components';
import { useDispatch, useSelector } from 'react-redux';
import { setResumeData as setStoreResumeData } from '@stores/actions/resume';
import { container, ui } from '@landrover/business/index';
import FormItem from '@components/FormItem';
import MonthPicker from '@components/MonthPicker';
import dayjs from 'dayjs';
import ArrowRightIcon from '@components/ArrowRightIcon';
import SectionFooter from '../SectionFooter';

type ProjectFormProps = {
  id: number
}
const fieldMap = {
  'name': '项目名称',
  'company': '公司名称',
  'position': '职位名称',
  'startTime': '开始时间',
  'endTime': '结束时间',
  'description': '项目描述',
  'responsibilities': '项目职责',
  'achievements': '项目业绩',
};
const ProjectForm: FC<ProjectFormProps> = ({ id }) => {
  const { dictArr = {} } = useSelector((state: any) => state.dict);
  const requiredFields = dictArr?.form?.resume?.required?.projects || [];
  const isEdit = !!id;
  const dispatch = useDispatch();
  const storeResumeData = useSelector((state: any) => state.resume);
  let currentProjectData: any = {};
  if (isEdit) {
    storeResumeData.projects.forEach((item: any) => {
      if (item.id === Number(id)) currentProjectData = item;
    });
  }

  const [projectData, setProjectData] = useState<ProjectTypes>(currentProjectData);

  const handleDelete = () => {
    container.saasService.deleteResumeSection('projects', id).then(() => {
      const newProjects = storeResumeData.projects.filter((item: any) => item.id !== Number(id));
      dispatch(setStoreResumeData({ ...storeResumeData, projects: newProjects }));
      Taro.navigateBack();
    });
  };

  const handleSubmit = () => {
    Taro.showLoading();

    // 过滤所有字符串类型的字段，去除前后空格
    const formattedData = {};
    for (const [key, value] of Object.entries(projectData)) {
      formattedData[key] = value;
      if (typeof value === 'string' && value !== value.trim()) {
        formattedData[key] = value.trim();
        setProjectData({ ...projectData, [key]: value.trim() });
      }
    }

    // 校验必填字段是否为空
    for (const key of requiredFields) {
      if (!formattedData[key]) {
        return ui.showToast(`${fieldMap[key]}不能为空`);
      }
    }

    const projectPromise = container.saasService[isEdit ? 'updateResumeSection' : 'addResumeSection']('projects', projectData);

    projectPromise.then((result: ResumeTypes) => {
      if (result) {
        let newProjects: ProjectTypes[];
        if (isEdit) {
          newProjects = [];
          storeResumeData.projects.forEach((item: ProjectTypes) => {
            if (item.id === projectData?.id) {
              newProjects.push(result);
            } else {
              newProjects.push(item);
            }
          });
        } else {
          newProjects = [result, ...storeResumeData.projects];
        }

        dispatch(setStoreResumeData({ ...storeResumeData, projects: newProjects }));
        Taro.navigateBack();
      }
    })
    .catch((res: any) => {
      return ui.showToast(res.errorMessage);
    });
  };

  const { name, position, company, startTime, endTime, responsibilities, description, achievements, soFar } = projectData || {};

  return (
    <View className="qz-ProjectForm">
      <Form onSubmit={handleSubmit}>
        <FormItem label="项目名称" required={requiredFields.includes('name')}>
          <Input
            name="name"
            className="qz-FormItem__value"
            type="text"
            value={name}
            onInput={(e) => {
              setProjectData({ ...projectData, name: e.detail.value });
            }}
            placeholder="请输入项目名称"
            placeholderClass="qz-FormItem__placeholder"
          />
        </FormItem>
        <FormItem label="公司名称" required={requiredFields.includes('company')}>
          <Input
            name="company"
            className="qz-FormItem__value"
            type="text"
            value={company}
            onInput={(e) => {
              setProjectData({ ...projectData, company: e.detail.value });
            }}
            placeholder="请输入公司名称"
            placeholderClass="qz-FormItem__placeholder"
          />
        </FormItem>
        <FormItem label="职位" required={requiredFields.includes('position')}>
          <Input
            name="position"
            className="qz-FormItem__value"
            type="text"
            value={position}
            onInput={(e) => {
              setProjectData({ ...projectData, position: e.detail.value });
            }}
            placeholder="请输入职位名称"
            placeholderClass="qz-FormItem__placeholder"
          />
        </FormItem>
        <FormItem label="开始时间" required={requiredFields.includes('startTime')}>
          <Block>
            <MonthPicker
              name="startTime"
              value={startTime ? dayjs(startTime).format('YYYY年M月') : ''}
              onChange={(values: any) => setProjectData({ ...projectData, ...values })}
            />
            <ArrowRightIcon />
          </Block>
        </FormItem>
        <FormItem label="结束时间" required={requiredFields.includes('endTime')}>
          <Block>
            <MonthPicker
              name="endTime"
              soFar={soFar || 0}
              value={endTime ? dayjs(endTime).format('YYYY年M月') : ''}
              onChange={(values: any) => setProjectData({ ...projectData, ...values })}
            />
            <ArrowRightIcon />
          </Block>
        </FormItem>
        <FormItem label="项目描述" required={requiredFields.includes('description')}>
          <Textarea
            placeholder="请输入项目描述"
            value={description}
            className="qz-FormItem__value"
            placeholderClass="qz-FormItem__placeholder"
            onInput={(e) =>
              setProjectData({ ...projectData, description: e.detail.value })
            }
          ></Textarea>
        </FormItem>
        <FormItem label="项目职责" required={requiredFields.includes('responsibilities')}>
          <Textarea
            placeholder="请输入项目职责"
            value={responsibilities}
            className="qz-FormItem__value"
            placeholderClass="qz-FormItem__placeholder"
            onInput={(e) =>
              setProjectData({ ...projectData, responsibilities: e.detail.value })
            }
          ></Textarea>
        </FormItem>
        <FormItem label="项目业绩" required={requiredFields.includes('achievements')}>
          <Textarea
            placeholder="请输入项目业绩"
            value={achievements}
            className="qz-FormItem__value"
            placeholderClass="qz-FormItem__placeholder"
            onInput={(e) =>
              setProjectData({ ...projectData, achievements: e.detail.value })
            }
          ></Textarea>
        </FormItem>
        <SectionFooter name="项目经历" isEdit={isEdit} onDelete={handleDelete} />
      </Form>
    </View>
  );
};
export default ProjectForm;
