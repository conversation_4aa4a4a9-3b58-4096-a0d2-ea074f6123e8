import type { FC } from 'react';
import { useState } from 'react';
import Taro from '@tarojs/taro';
import { View, Input, Block, Form } from '@tarojs/components';
import NativePicker from '@components/NativePicker';
import QzButton from '@components/Button';
import { useDispatch, useSelector } from 'react-redux';
import { setResumeData as setStoreResumeData } from '@stores/actions/resume';
import { container, ui } from '@landrover/business/index';
import Footer from '@components/Footer';
import FormItem from '@components/FormItem';
import ArrowRightIcon from '@components/ArrowRightIcon';

type BasicFormProps = {}
let defaultBirthDate = new Date();
defaultBirthDate.setFullYear(defaultBirthDate.getFullYear() - 23);
const fieldMap = {
  'name': '姓名',
  'phone': '手机号',
  'gender': '性别',
  'card': '身份证',
  'birth': '出生年月',
  'wechat': '微信',
  'school': '毕业学校',
  'degree': '学历',
};
const BasicForm: FC<BasicFormProps> = () => {
  const { dictArr = {} } = useSelector((state: any) => state.dict);
  const genderOptions = dictArr?.common?.gender;
  const degreeOptions = dictArr?.common?.degree;
  const requiredFields = dictArr?.form?.resume?.required?.basic || [];
  const dispatch = useDispatch();
  const storeResumeData = useSelector((state: any) => state.resume);
  const [resumeData, setResumeData] = useState<ResumeTypes>(storeResumeData);

  const handleSubmit = () => {
    Taro.showLoading();

    // 过滤所有字符串类型的字段，去除前后空格
    const formattedData = {};
    for (const [key, value] of Object.entries(resumeData)) {
      formattedData[key] = value;
      if (typeof value === 'string' && value !== value.trim()) {
        formattedData[key] = value.trim();
        setResumeData({ ...resumeData, [key]: value.trim() });
      }
    }

    // 校验必填字段是否为空
    for (const key of requiredFields) {
      if (!formattedData[key]) {
        return ui.showToast(`${fieldMap[key]}不能为空`);
      }
    }

    container.saasService.editBaseResume(resumeData)
    .then((result: ResumeTypes) => {
      if (result) {
        dispatch(setStoreResumeData({ ...storeResumeData, ...result }));
        Taro.navigateBack();
      }
    })
    .catch((res: any) => ui.showToast(res.errorMessage));
  };

  const { name, phone, gender, card, birth, wechat, school, degree } = resumeData;

  return (
    <View className="qz-BasicForm">
      <Form onSubmit={handleSubmit}>
        <FormItem label="姓名" required={requiredFields.includes('name')}>
          <Input
            name="name"
            className="qz-FormItem__value"
            type="text"
            value={name}
            onInput={(e): void => {
              setResumeData({ ...resumeData, name: e.detail.value });
            }}
            placeholder="请输入姓名"
            placeholderClass="qz-FormItem__placeholder"
          />
        </FormItem>
        <FormItem label="手机号" required={requiredFields.includes('phone')}>
          <Input
            name="phone"
            className="qz-FormItem__value"
            type="text"
            value={phone}
            onInput={(e): void => {
              setResumeData({ ...resumeData, phone: e.detail.value });
            }}
            placeholder="请输入手机号"
            placeholderClass="qz-FormItem__placeholder"
          />
        </FormItem>
        <FormItem label="性别" required={requiredFields.includes('gender')}>
          <Block>
            <NativePicker
              range={genderOptions}
              value={gender}
              name="gender"
              mode="selector"
              onChange={(val: any): void =>
                setResumeData({ ...resumeData, gender: val.id })
              }
              placeholder="请选择性别"
            />
            <ArrowRightIcon />
          </Block>
        </FormItem>
        <FormItem label="身份证" required={requiredFields.includes('card')}>
          <Input
            name="card"
            className="qz-FormItem__value"
            type="text"
            value={card}
            onInput={(e): void => {
              setResumeData({ ...resumeData, card: e.detail.value });
            }}
            placeholder="请输入身份证"
            placeholderClass="qz-FormItem__placeholder"
          />
        </FormItem>
        <FormItem label="出生年月" required={requiredFields.includes('birth')}>
          <NativePicker
            fieldProps={{
              fields: 'month',
            }}
            value={birth}
            mode="date"
            name="birth"
            defaultBirthDate={defaultBirthDate.toJSON().substring(0, 7)}
            end={new Date().toJSON().substring(0, 7)}
            onChange={(val: any): void => {
              setResumeData({ ...resumeData, birth: val });
            }}
            placeholder="请选择出生年月"
          />
        </FormItem>

        <FormItem label="毕业学校" required={requiredFields.includes('school')}>
          <Input
            name="school"
            type="text"
            value={school}
            className="qz-FormItem__value"
            placeholder="请输入毕业学校"
            placeholderClass="qz-ListItem-placeholder"
            onInput={(e): void => {
              setResumeData({ ...resumeData, school: e.detail.value });
            }}
          />
        </FormItem>
        <FormItem label="学历" required={requiredFields.includes('degree')}>
          <Block>
            <NativePicker
              range={degreeOptions}
              value={degree}
              name="degree"
              mode="selector"
              onChange={(val: any): void =>
                setResumeData({ ...resumeData, degree: val.id })
              }
              placeholder="请选择学历"
            />
            <ArrowRightIcon />
          </Block>
        </FormItem>

        <FormItem label="微信号" required={requiredFields.includes('wechat')}>
          <Input
            name="wechat"
            className="qz-FormItem__value"
            type="text"
            value={wechat}
            onInput={(e): void => {
              setResumeData({ ...resumeData, wechat: e.detail.value });
            }}
            placeholder="请输入微信号"
            placeholderClass="qz-FormItem__placeholder"
          />
        </FormItem>

        <Footer className="position-details__footer">
          <QzButton
            full
            formType="submit"
            className="position-details__submit"
            circle
            type="secondary"
          >
            保存
          </QzButton>
        </Footer>
      </Form>
    </View>
  );
};
export default BasicForm;
