import type { FC } from 'react';
import { useState } from 'react';
import Taro from '@tarojs/taro';
import { View, Form, Textarea } from '@tarojs/components';
import QzButton from '@components/Button';
import { useDispatch, useSelector } from 'react-redux';
import { setResumeData as setStoreResumeData } from '@stores/actions/resume';
import { container, ui } from '@landrover/business/index';
import Footer from '@components/Footer';
import FormItem from '@components/FormItem';

type SelfRemarkFormProps = {}
const SelfRemarkForm: FC<SelfRemarkFormProps> = () => {
  const dispatch = useDispatch();
  const storeResumeData = useSelector((state: any) => state.resume);
  const [resumeData, setResumeData] = useState<ResumeTypes>(storeResumeData);
  const handleSubmit = () => {
    Taro.showLoading();

    container.saasService.editBaseResume(resumeData)
    .then((result: ResumeTypes) => {
      if (result) {
        dispatch(setStoreResumeData({ ...storeResumeData, ...result }));
        Taro.navigateBack();
      }
    })
    .catch((res: any) => {
      return ui.showToast(res.errorMessage);
    });
  };

  const { selfRemark } = resumeData;
  return (
    <View className="qz-SelfRemarkForm">
      <Form onSubmit={handleSubmit}>
        <FormItem label="自我评价">
          <Textarea
            placeholder="请输入自我评价"
            value={selfRemark}
            className="qz-FormItem__value"
            placeholderClass="qz-FormItem__placeholder"
            onInput={(e) =>
              setResumeData({ ...resumeData, selfRemark: e.detail.value })
            }
          ></Textarea>
        </FormItem>
        <Footer>
          <QzButton
            full
            formType="submit"
            circle
            type="secondary"
          >
            保存
          </QzButton>
        </Footer>
      </Form>
    </View>
  );
};
export default SelfRemarkForm;
