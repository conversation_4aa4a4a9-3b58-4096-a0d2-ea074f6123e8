import Taro, { getCurrentInstance } from '@tarojs/taro';
import type { FC } from 'react';
import { View } from '@tarojs/components';
import SeekPurposeForm from './SeekPurposeForm';
import BasicForm from './BasicForm';
import EducationForm from './EducationForm';
import WorkForm from './WorkForm';
import ProjectForm from './ProjectForm';
import SelfRemarkForm from './SelfRemarkForm';

type EditProps = {}
const Edit: FC<EditProps> = () => {
  const ComponentMap = {
    'basic': {
      title: '基本信息',
      comp: BasicForm,
    },
    'seekPurpose': {
      title: '求职意向',
      comp: SeekPurposeForm,
    },
    'education': {
      title: '教育经历',
      comp: EducationForm,
    },
    'work': {
      title: '工作经历',
      comp: WorkForm,
    },
    'project': {
      title: '项目经历',
      comp: ProjectForm,
    },
    'selfRemark': {
      title: '自我评价',
      comp: SelfRemarkForm,
    },
  };
  const { router } = getCurrentInstance();
  const { type, id } = router?.params || {};
  const WrapComponent = ComponentMap[type || 'seekPurpose']['comp'];
  Taro.setNavigationBarTitle({
    title: ComponentMap[type || 'seekPurpose']['title'],
  });
  return (
    <View style={{
      paddingBottom: '160px',
    }}
    >
      <WrapComponent id={id} />
    </View>
  );
};
export default Edit;
