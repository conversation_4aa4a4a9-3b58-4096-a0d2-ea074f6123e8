import QzButton from '@components/Button';
import CustomNav from '@components/CustomNav';
import Footer from '@components/Footer';
import QzIcon from '@components/Icon';
import Text from '@components/qz/Text';
import Title from '@components/qz/Title';
import { Block, Form, Input, View } from '@tarojs/components';
import { container, ui } from '@landrover/business/index';
import Taro, { getCurrentInstance } from '@tarojs/taro';
import { FC, useEffect, useState } from 'react';
import List from '@components/List';
import ListItem from '@components/List/components/ListItem';
import NativePicker from '@components/NativePicker';
import { useSelector } from 'react-redux';
import './complete.scss';

let defaultBirthDate = new Date();
defaultBirthDate.setFullYear(defaultBirthDate.getFullYear() - 23);

type ResumeCompleteProps = {};
const ResumeComplete: FC<ResumeCompleteProps> = () => {
  const userInfo = container.userService.auth || {};
  const [resumeData, setResumeData] = useState<any>({ gender: 0, degree: 0 });

  const { MENU_BUTTON, SCREEN_WIDTH } = Taro.getApp().$app;
  const { right, bottom } = MENU_BUTTON;
  const NAV_GAP = SCREEN_WIDTH - right;
  const navbarHeight = bottom + NAV_GAP;
  const { router } = getCurrentInstance();
  const positionId = Number(router?.params?.positionId);
  const { dictArr = {} } = useSelector((state: any) => state.dict);
  const genderOptions = dictArr?.common?.gender;
  const degreeOptions = dictArr?.common?.degree;

  useEffect(() => {
    container.saasService.getResumeDetail(0).then((data: any) => {
      setResumeData(Object.fromEntries(['name', 'gender', 'birth', 'degree', 'school'].map(key => ([key, data[key]]))));
    });
  }, []);

  const deliver = async () => {
    await container.saasService.updateFlow({ positionId });
    // 数据传递给上一个页面，标记为已投递
    const pages = Taro.getCurrentPages();
    const current = pages[pages.length - 2]; /* 指向上一个页面 */
    current.setData({ id: positionId });
    // 关闭loading
    Taro.hideLoading();
    // 报名成功后的处理
    // 1. 未订阅公众号，跳转到公众号关注页面
    // 2. 已订阅公众号，弹出报名成功提示后返回上一页
    const pOcSubscribe = userInfo.pOcSubscribe;
    if (!pOcSubscribe) {
      // 使用redirectTo，防止用户报名成功后返回当前编辑页面
      Taro.redirectTo({ url: `/packageA/pages/result` });
    } else {
      Taro.showToast({
        title: '报名成功',
        icon: 'success',
        duration: 2000,
      });
      setTimeout(() => Taro.navigateBack(), 2000);
    }
  };

  const creatResumeAndDeliver = () => {
    Taro.showLoading();
    container.saasService.editBaseResume(resumeData).then(deliver).catch(() => Taro.hideLoading());
  };

  // const handleConfirmUpload = async (files: any) => {
  //
  //   if (!files) {
  //     return ui.showToast('请选择要导入的文件');
  //   }

  // container.saasService
  //               .fileUpload("file", avatarUrl, {
  //                 category: "avatar"
  //               })
  //               .then(res => {
  //                 setUserInfo({ ...userInfo, avatar: res.url });
  //               });

  // await ui.showLoading('上传中');
  // container.saasService.fileUpload('file', files[0].path, {
  //   category: 'resumeFile',
  // }).then(() => ui.showToast('导入成功', 'success')).catch(() => ui.showToast('导入失败', 'error'));


  // Promise.all(promise)
  //   .then((res: any[]) => {
  //     const repeatList: any[] = res.filter(item => !!item.__REPEAT__);
  //     const successList: any[] = res.filter(item => !item.__REPEAT__);
  //     if (!repeatList.length) {
  //       const newData = successList.concat(data);
  //       onChangeResumeData && onChangeResumeData(newData);
  //       ui.showToast("导入成功", "success");
  //     } else {
  //       if (!successList.length) {
  //         ui.showToast("简历已存在", "error");
  //       } else {
  //         const newData = successList.concat(data);
  //         onChangeResumeData && onChangeResumeData(newData);
  //         ui.showToast("导入成功", "success");
  //       }
  //     }
  //   })
  //   .catch(() => {
  //     ui.showToast("导入失败", "error");
  //   });
  // };

  // const handleImport = () => {
  //   wx.chooseMessageFile({
  //     count: 1,
  //     type: 'file',
  //     success(res) {
  //       // tempFilePath可以作为img标签的src属性显示图片
  //       const tempFilePaths = res.tempFiles;
  //       handleConfirmUpload(tempFilePaths);
  //     },
  //   });
  // };

  const { name, gender, birth, degree, school } = resumeData;

  return (
    <View
      className="container container-complete"
      style={{
        paddingTop: navbarHeight + 16,
      }}
    >
      <CustomNav />
      <Block>
        <Title level={3}>请先完善基本信息</Title>
        <View>
          <Text type="secondary">
            基本信息用于生成在线简历
            {/* ，如已有电子简历， */}
          </Text>
          {/* <Text type="secondary">
              请点击此处{" "}
              <View
                onClick={handleImport}
                style={{
                  color: primaryColor,
                  display: "inline-flex"
                }}
              >
                上传电子简历
              </View>
            </Text> */}
        </View>

        <Form
          className="form-resume-complete"
          onSubmit={() => {
            if (!name) {
              return ui.showToast('请输入姓名');
            }
            if (!gender) {
              return ui.showToast('请选择性别');
            }
            if (!birth) {
              return ui.showToast('请选择出生年月');
            }
            creatResumeAndDeliver();
          }}
        >
          <List>
            <ListItem label="姓名" required>
              <Input
                name="name"
                value={name}
                type="text"
                className="qz-FormItem__value"
                onInput={(e): void => {
                  setResumeData({ ...resumeData, name: e.detail.value });
                }}
                placeholder="请输入姓名"
                placeholderClass="qz-ListItem-placeholder"
              />
            </ListItem>

            <ListItem label="性别" required>
              <NativePicker
                range={genderOptions}
                value={gender}
                name="gender"
                mode="selector"
                onChange={(val: any) => {
                  setResumeData({ ...resumeData, gender: val.id });
                }}
                placeholder="请选择性别"
              />
              <QzIcon type="icon_arrow" size="18" />
            </ListItem>
            <ListItem label="出生年月" required>
              <Block>
                <NativePicker
                  fieldProps={{
                    fields: 'month',
                  }}
                  mode="date"
                  name="birth"
                  value={birth}
                  end={new Date().toJSON().substring(0, 7)}
                  onChange={(val): void => {
                    setResumeData({ ...resumeData, birth: val });
                  }}
                  placeholder="请选择出生年月"
                />

                <QzIcon type="icon_arrow" size="18" />
              </Block>
            </ListItem>
            <ListItem label="毕业学校">
              <Input
                name="school"
                type="text"
                value={school}
                className="qz-FormItem__value"
                placeholder="请输入毕业学校"
                placeholderClass="qz-ListItem-placeholder"
                onInput={(e): void => {
                  setResumeData({ ...resumeData, school: e.detail.value });
                }}
              />
            </ListItem>
            <ListItem label="学历">
              <Block>
                <NativePicker
                  range={degreeOptions}
                  value={degree}
                  name="degree"
                  mode="selector"
                  onChange={(val: any) => {
                    setResumeData({ ...resumeData, degree: val.id });
                  }}
                  placeholder="请选择学历"
                />
                <QzIcon type="icon_arrow" size="18" />
              </Block>
            </ListItem>
          </List>

          <Footer>
            <Block>
              <QzButton
                formType="submit"
                customStyle={{
                  height: '48px',
                  borderRadius: '48px',
                }}
                type="primary"
                full
                circle
              >
                保存并报名
              </QzButton>
            </Block>
          </Footer>
        </Form>
      </Block>
    </View>
  );
};
export default ResumeComplete;
