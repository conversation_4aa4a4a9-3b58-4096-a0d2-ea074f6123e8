import Desc from '@components/Desc';
import JumpTitle from '@components/JumpTitle';
import ResumeEditCard from '@components/ResumeEditCard';
import { Block, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { formatRangePicker } from '@utils/format';
import { useSelector } from 'react-redux';
import React from 'react';
import './index.scss';

type EducationCardProps = {
  resume: ResumeTypes;
};
const EducationCard: React.FC<EducationCardProps> = ({ resume }) => {
  const { dictMap = {} } = useSelector((state: any) => state.dict);
  const { educations } = resume;
  const getItemDesc = (item: any) => {
    let info: string[] = [];
    const { degree, discipline, startTime, endTime, soFar } = item;
    const time = formatRangePicker(startTime, endTime, soFar);
    if (time) {
      info.push(time);
    }
    if (degree && dictMap?.common?.degree[degree]) {
      info.push(dictMap?.common?.degree[degree]);
    }
    if (discipline) {
      info.push(discipline);
    }
    return info;
  };

  return (
    <View className="qz-EducationCard">
      <ResumeEditCard title="教育经历" onEdit={() => Taro.navigateTo({ url: `/packageA/pages/resume/edit?type=education` })}>
        {educations.length ? <Block>
          {educations.map(item => {
            const desc = getItemDesc(item);
            return (
              <View key={item.id}>
                <JumpTitle
                  onClick={() => {
                    Taro.navigateTo({
                      url: `/packageA/pages/resume/edit?type=education&id=${item.id}`,
                    });
                  }}
                  title={
                    <View>{item.school}</View>
                  }
                />
                <Desc>
                  <View className="qz-EducationCard__desc">
                    {desc.map((info: string) => {
                      return <Text key={info}>{info}</Text>;
                    })}
                  </View>
                </Desc>
              </View>
            );
          })}
        </Block> : <Desc>
          暂无教育经历，请添加教育经历
        </Desc>}

      </ResumeEditCard>
    </View>
  );
};

export default EducationCard;
