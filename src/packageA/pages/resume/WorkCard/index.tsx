import Desc from '@components/Desc';
import JumpTitle from '@components/JumpTitle';
import ResumeEditCard from '@components/ResumeEditCard';
import { Block, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { formatRangePicker } from '@utils/format';
import React from 'react';
import './index.scss';

type WorkCardProps = {
  resume: ResumeTypes;
};
const WorkCard: React.FC<WorkCardProps> = ({ resume }) => {
  const { works } = resume;
  const getTimeRange = (item: any) => {
    const { startTime, endTime, soFar } = item;
    return formatRangePicker(startTime, endTime, soFar);
  };

  return (
    <View className="qz-WorkCard">
      <ResumeEditCard title="工作经历" onEdit={() => Taro.navigateTo({ url: `/packageA/pages/resume/edit?type=work` })}>
        <View className="qz-WorkCard__content">
          {works.length ? <Block>{works.map(item => {
            const { company, position, description, achievements } = item;
            const timeRange = getTimeRange(item);
            return (
              <View key={item.id} className="qz-WorkCard__content-item">
                <JumpTitle
                  onClick={() => {
                    Taro.navigateTo({
                      url: `/packageA/pages/resume/edit?type=work&id=${item.id}`,
                    });
                  }}
                  title={
                    <View>{company}</View>
                  }
                />
                <Desc style={{ display: 'flex', gap: '8px' }}>
                  {timeRange && <Text>{timeRange}</Text>}
                  <Text>{position}</Text>
                </Desc>
                {description && (<Desc direction="column" style={{ gap: '0px' }}>
                  <Text>工作内容:</Text>
                  <Text>{description}</Text>
                </Desc>)}
                {achievements && (<Desc direction="column" style={{ gap: '0px' }}>
                  <Text>工作业绩:</Text>
                  <Text>{achievements}</Text>
                </Desc>)}
              </View>
            );
          })}</Block> : <Desc>
            暂无工作经历，请添加工作经历
          </Desc>}

        </View>
      </ResumeEditCard>
    </View>
  );
};

export default WorkCard;
