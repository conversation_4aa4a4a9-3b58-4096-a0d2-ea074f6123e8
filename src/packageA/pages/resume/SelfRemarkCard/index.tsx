import Desc from "@components/Desc";
import ResumeEditCard from "@components/ResumeEditCard";
import Taro from "@tarojs/taro";
import React from 'react';

type SelfRemarkCardProps = {
  resume: ResumeTypes;
};
const SelfRemarkCard: React.FC<SelfRemarkCardProps> = ({ resume }) => {
  const { selfRemark } = resume;
  return (
    <ResumeEditCard
      title="自我评价"
      type="edit"
      onEdit={() => {
        Taro.navigateTo({
          url: `/packageA/pages/resume/edit?type=selfRemark`
        });
      }}
    >
      {selfRemark ? <Desc>{selfRemark}</Desc> : <Desc>80%的招聘方对此感兴趣</Desc>}
    </ResumeEditCard>
  );
};

export default SelfRemarkCard;
