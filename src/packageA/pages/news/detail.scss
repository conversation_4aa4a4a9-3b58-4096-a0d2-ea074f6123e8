@import '@styles/variables/default.scss';

.qz-NewsDetail{
  padding: 0 15px;
  &__title{
    font-size: 24px;
  }
  &__createdAt{
    margin-top: 16px;
    color:$color-text-secondary;
  }
  &__content{
    margin-top: 16px;
    line-height: 1.5;
  }
  //image{
  //  width: 100%;
  //}
  //.h5{
  //  &-h1,&-h2,&-h3,&-h4,&-h5{
  //    margin-bottom:0.5em;
  //    text-indent: 2em;
  //    color: rgba(0, 0, 0, 0.85);
  //    font-weight: 500;
  //  }
  //  &-br{
  //    display: inline-block;
  //    width:100%;
  //    height: 16px;
  //  }
  //  &-p{
  //    margin-bottom: 1em;
  //  }
  //  &-strong{
  //    font-weight: bold;
  //  }
  //  &-h1{
  //    font-size: 2em;
  //  }
  //  &-h2{
  //    font-size: 1.5em;
  //  }
  //  &-h3{
  //    font-size: 1.17em;
  //  }
  //  &-h4{
  //    font-size: 1em;
  //  }
  //  &-h5{
  //    font-size: 0.83em;
  //  }
  //}
}
