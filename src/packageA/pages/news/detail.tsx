import { getCurrentInstance } from '@tarojs/taro';
import { FC, useEffect, useState } from 'react';
import { container } from "@landrover/business/index";
import { RichText, View } from '@tarojs/components';
import dayjs from 'dayjs';
import './detail.scss';

type NewsDetailProps = {}
const NewsDetail: FC<NewsDetailProps> = () => {
  const { router } = getCurrentInstance();
  const id = router?.params?.id;

  const [detailData, setDetailData] = useState<any>();

  useEffect(() => {
    container.saasService.getNewsDetail(Number(id)).then((res: any) => setDetailData(res));
  }, [id]);

  if (!detailData) return null;

  let { title, content, createdAt } = detailData;
  wx.setNavigationBarTitle({ title });
  content = content.replace(/<img/g, '<img style="max-width:100%;height:auto"');

  return (
    <View className="qz-NewsDetail">
      <View className="qz-NewsDetail__title">{title}</View>
      <View className="qz-NewsDetail__createdAt">发布时间：{dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss')}</View>
      <View className="qz-NewsDetail__content">
        <RichText nodes={content} />
      </View>
    </View>
  );
};
export default NewsDetail;
