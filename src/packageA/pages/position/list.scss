.container-position-list{
  min-height: 100vh;
  background-color: #f8f8fa;
  &-pickerGroup{
    &.fixed{
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 9;
    }
  }
  &-classic{
    .container-position-list__content{
      border-radius: 10px;
      overflow: hidden;
    }
  }
  &__content{
    padding: 15px;
  }
  .qz-positionList-default.qz-positionList .qz-positionList__inner-card{
    padding:0;
  }
}
