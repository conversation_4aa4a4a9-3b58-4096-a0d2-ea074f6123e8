import React, { FC, useCallback, useEffect, useState } from 'react';
import { container, ui } from '@landrover/business';
import Taro, { getCurrentInstance, usePageScroll, usePullDownRefresh, useReachBottom, useShareAppMessage } from '@tarojs/taro';
import { View } from '@tarojs/components';
import Loading from '@components/Loading';
import PositionList from '@components/qz/PositionList';
import SharePageContainer from '@components/SharePageContainer';
import { useDispatch, useSelector } from 'react-redux';
import { setShareData } from '@stores/actions/share';
import { stringify, parse } from 'querystring';
import imageUrl from '@images/share.jpg';
import PositionFilterPageContainer from '@components/qz/PositionFilterPageContainer';
import PickerGroup from '@components/PickerGroup';
import classNames from 'classnames';
import './list.scss';

type HomeProps = {};
const Home: FC<HomeProps> = () => {
  const { extConfig, TPL_CONFIG } = Taro.getApp().$app;
  const themeValue = TPL_CONFIG.theme.value;
  const [filterValues, setFilterValues] = useState<any>({});
  const [positionFilterFixed, setPositionFilterFixed] = useState<boolean>(false);
  const [filterVisible, setFilterVisible] = useState(false);
  const [shareImageUrl, setShareImageUrl] = useState<string>('');
  const dispatch = useDispatch();
  const shareData = useSelector((state: any) => state.share);
  const positionData = shareData?.position;

  const [loading, setLoading] = useState<boolean>(true);
  const [scrollComplete, setScrollComplete] = useState<boolean>(false);
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [totalNumber, setTotalNumber] = useState<number>(0);
  const [listData, setListData] = useState<PositionTypes[]>([]);

  const { router } = getCurrentInstance();
  const { pageTitle, search } = router?.params || {};

  Taro.setBackgroundTextStyle({ textStyle: 'dark' });
  Taro.setNavigationBarTitle({ title: pageTitle || '职位列表' });

  usePageScroll((e) => setPositionFilterFixed(e.scrollTop > 0));

  useShareAppMessage(() => {
    if (shareData?.visible) {
      const shareParams = {
        id: positionData?.id,
        invitor: Taro.getStorageSync('invitor') || undefined,
      };
      return {
        title: `${positionData?.client?.shortName || positionData?.client?.name}·${positionData?.name}`,
        path: '/packageA/pages/position/detail?' + stringify(shareParams),
        imageUrl: shareImageUrl,
      };
    } else {
      return {
        title: `上${extConfig.nickname}，轻松找到好工作`,
        path: '/pages/index/index',
        imageUrl: imageUrl,
      };
    }
  });

  const getPositionList = useCallback((params: { current: number; }, callback?: any) => {
    const searchObject = parse(decodeURIComponent(search || ''));
    if (Array.isArray(searchObject.tagIds)) {
      searchObject.tagIds = searchObject.tagIds.join(',');
    }
    container.saasService.getPositions({ pageSize: 10, ...params, ...searchObject, ...filterValues }).then((result: { list: any[]; current: number; total: number }) => {
      const { current, list, total } = result;

      setLoading(false);
      setPageIndex(current);
      setListData(current === 1 ? list : data => data.concat(list));
      setTotalNumber(total);
      callback && callback();
    });
  }, [filterValues, search]);

  useEffect(() => getPositionList({ current: 1 }), [getPositionList]);

  useReachBottom(() => {
    const getLastPageIndex = Math.ceil(totalNumber / 10);

    // 判断是否有下一页数据
    const hasNext = pageIndex < getLastPageIndex;
    // 没有下一页，无数加载更多数据
    if (!hasNext) {
      setScrollComplete(true);
      return;
    }

    getPositionList({
      current: pageIndex + 1,
    });
  });

  const refreshCallback = () => {
    setTimeout(() => {
      Taro.stopPullDownRefresh();
      return ui.showToast('职位已更新', 'none', 1000);
    }, 500);
  };

  usePullDownRefresh(() => getPositionList({ current: 1 }, refreshCallback));

  const handleUpdateListData = (id: number) => {
    const data = [...listData];
    data.forEach(item => {
      if (item.id === id) {
        item.flow = {
          id,
        };
      }
    });
    setListData(data);
  };

  const containerStyle: React.CSSProperties = {};
  if (positionFilterFixed) {
    containerStyle.paddingTop = '50px';
  }
  const pickerStyle = { fixed: positionFilterFixed };

  return (
    <View className={`container-position-list container-position-list-${themeValue}`} style={containerStyle}>
      <View className={classNames('container-position-list-pickerGroup', pickerStyle)}>
        <PickerGroup filterValues={filterValues} onChangeFilterValues={setFilterValues} />
      </View>
      {loading && <Loading />}
      {!loading && (
        <View className="container-position-list__content">
          <PositionList data={listData} onChangeListData={handleUpdateListData} scrollComplete={scrollComplete} />
        </View>
      )}
      <SharePageContainer onChangeShareImg={(url) => setShareImageUrl(url)} onCancel={() => dispatch(setShareData({ visible: false }))} visible={shareData?.visible} />
      <PositionFilterPageContainer config={TPL_CONFIG} values={filterValues} onChange={setFilterValues} visible={filterVisible} onCancel={() => setFilterVisible(false)} />
    </View>
  );
};

export default Home;
