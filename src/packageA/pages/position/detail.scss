@import '@styles/variables/default';
@import '@styles/mixins/index';


.order-share{
    position: relative;
    border:none;
    border-width: 0;
    background-color: transparent;
    padding-left: 22px;
    font-size: 16px;
    color: #656565;
    &__icon {
      @include absolute-center(absolute, vertical);
      left: 0;
    }
    &::after{
      border-width: 0;
    }
}
.container-position-detail{
  background-color: $page-bg;
  min-height: 100vh;
  .position-detail-container{
    // padding-top: 60px;
    position: relative;
    padding-bottom: 120px;
    .position-details__footer.footer-inner{
      align-items: flex-start;
      padding-top: 15px;
    }
    .position-details__kefu{
      display: flex;
      flex-direction: column;
      margin-top: -30px;
      border: 'none';
      border-width: 0;
      padding: 0;
      background-color: transparent;
      line-height: inherit;
      &::after{
        border-width: 0;
      }
      .kefu-avatar{
        border-radius: 48px;
        border: solid 0.5px #eee;
        overflow: hidden;
      }
      .kefu-name{
        font-size: 14px;
        font-weight: 400;
      }
      .kefu-status{
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        color: #656565;
        font-weight: 200;
      }
      .kefu-dot{
        position: absolute;
        right: 0;
        bottom: 0;
        display: inline-block;
        width: 10px;
        height: 10px;
        border: solid 2px white;
        background-color: #61d086;
        border-radius: 10px;
      }
    }


    .position-details__footer {
      justify-content: space-between !important;
      eui-button {
        flex: 1;
      }
    }
    .position-details__submit {
      width: 90px !important;
      // margin-left: 10px;
      flex: 1;
    }
    .position-details__chat {
      width: 90px !important;
      // margin-left: 10px;
      flex: 1;
    }
    .position-detail-header{
      position: fixed;
      left:0;
      top:0;
      display: flex;
      width: 100%;
      justify-content: space-between;
      align-items: center;
      z-index:99;
      // background-color: #f8f8f8;
      // border-bottom: solid 0.5px #eee;
    }
  }
}


.flip-vertical-left {
	-webkit-animation: flip-vertical-left 0.4s cubic-bezier(0.455, 0.030, 0.515, 0.955) infinite both;
	        animation: flip-vertical-left 0.4s cubic-bezier(0.455, 0.030, 0.515, 0.955) infinite both;
}

/* ----------------------------------------------
 * Generated by Animista on 2022-3-26 21:51:46
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info.
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation flip-vertical-left
 * ----------------------------------------
 */
 @-webkit-keyframes flip-vertical-left {
  0% {
    -webkit-transform: rotateY(0);
            transform: rotateY(0);
  }
  100% {
    -webkit-transform: rotateY(-180deg);
            transform: rotateY(-180deg);
  }
}
@keyframes flip-vertical-left {
  0% {
    -webkit-transform: rotateY(0);
            transform: rotateY(0);
  }
  100% {
    -webkit-transform: rotateY(-180deg);
            transform: rotateY(-180deg);
  }
}
