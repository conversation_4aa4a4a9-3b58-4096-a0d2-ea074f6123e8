import Taro, { getCurrentInstance, useDidShow, useShareAppMessage } from '@tarojs/taro';
import React, { useEffect, useState } from 'react';
import type { FC } from 'react';
import { container } from '@landrover/business';
import { Block, View } from '@tarojs/components';
import Panel from '@components/Panel';
import PositionHeader from '@components/PositionHeader';
import Footer from '@components/Footer';
import QzButton from '@components/Button';
import DeliverButton from '@components/DeliverButton';
import Tag from '@components/Tag';
import { stringify } from 'querystring';
import { urlSearchToObject } from '@utils/format';
import ClientCard from '@components/ClientCard';
import Loading from '@components/Loading';
import CustomNav from '@components/CustomNav';
import SharePageContainer from '@components/SharePageContainer';
import { useDispatch, useSelector } from 'react-redux';
import { setShareData } from '@stores/actions/share';
import ScrollGallery from '@components/ScrollGallery';
import SwiperGallery from '@components/SwiperGallery';
import PositionTrack from '@components/PositionTrack';
import AddressNavigation from '@components/AddressNavigation';
import ShareAndFav from '@components/ShareAndFav';
import PositionInterviewer from '@components/PositionInterviewer';
import eventWatch from '@utils/watch';
import './detail.scss';

type PositionDetailProps = {}
const PositionDetail: FC<PositionDetailProps> = () => {
  const { mode = 'list' } = useSelector((state: any) => state.list);
  const dispatch = useDispatch();
  const storePositionData = useSelector((state: any) => state.position);
  const loginData = useSelector((state: any) => state.login);
  const shareData = useSelector((state: any) => state.share);
  const { isLogin } = loginData;

  const { TPL_CONFIG, MENU_BUTTON, SCREEN_WIDTH } = Taro.getApp().$app;
  const { right, bottom } = MENU_BUTTON;
  const NAV_GAP = SCREEN_WIDTH - right;
  const navbarHeight = bottom + NAV_GAP;
  const [imageUrl, setImageUrl] = useState<string>(require('@images/share.jpg'));
  const { router } = getCurrentInstance();

  const params = router?.params || {};
  const sceneObj = params?.scene ? urlSearchToObject(decodeURIComponent(params.scene)) : {};
  const mergeParams = { ...params, ...sceneObj };

  const { id, fId, s } = mergeParams;

  const invitor = s || Taro.getStorageSync('invitor');
  if (s) {
    Taro.setStorageSync('invitor', invitor);
  }

  const [trackId, setTrackId] = useState<number>();
  const [config, setConfig] = useState<any>();

  useEffect(() => {
    if (TPL_CONFIG) {
      setConfig(TPL_CONFIG);
    } else {
      eventWatch.one('CONFIG_READY', (res: any) => setConfig(res), [true]);
    }
  }, [TPL_CONFIG]);

  // 获取职位数据
  const [positionData, setPositionData] = useState<PositionTypes>();
  useEffect(() => {
    if (isLogin) {
      if (id) {
        if (Number(id) === storePositionData?.id) {
          setPositionData(storePositionData);
        } else {
          container.saasService.getPositionDetail(id).then((res: any) => {
            setPositionData(res);
          });
        }

      }
    }
  }, [id, isLogin, storePositionData]);


  // 进入页面时调用统计接口
  // invitor代表分享源（顾问），如果不传代表为候选人分享
  useEffect(() => {
    if (isLogin) {
      const trackParams: any = {};
      if (fId) {
        trackParams.viewFromId = fId;
      }
      if (invitor) {
        trackParams.invitor = invitor;
      }
      (async () => {
        container.saasService.trackPosition(id, trackParams).then((res: any) => {
          setTrackId(res.id);
        });
      })();
    }
  }, [fId, id, invitor, isLogin]);

  // 离开页面时调用统计接口
  useEffect(() => {
    if (trackId) {
      return () => {
        container.saasService.trackPosition(id, { viewEndId: trackId }).then(() => {
          console.log('离开');
        });
      };
    }
  }, [id, trackId]);

  // 离开页面时调用统计接口
  const [trackData, setTrackData] = useState<any>();
  useEffect(() => {
    if (id) {
      container.saasService.getPositionTrackData(id).then((res: any) => {
        setTrackData(res);
      });
    }
  }, [id]);

  // 设置分享
  const shareParams = {
    id,
    fId: trackId,
  };
  useShareAppMessage(() => {
    return {
      title: `${positionData?.client?.shortName || positionData?.client?.name}·${positionData?.name}`,
      path: '/packageA/pages/position/detail?' + stringify(shareParams),
      imageUrl,
    };
  });

  useDidShow(() => {
    const pages = Taro.getCurrentPages();
    let currentPage = pages[pages.length - 1]; /* 指向当前界面 */
    if (currentPage?.data?.id) {
      updateFlowStatus();
    }
  });

  const updateFlowStatus = () => {
    const data = { ...positionData };
    data.flow = {
      id,
    };
    setPositionData(data as PositionTypes);
  };

  if (!config) return <View style={{ paddingTop: navbarHeight }}><Loading /></View>;
  const themeValue = config.theme.value;

  if (positionData?.id) {
    const { interviewer, flow, advantages, client, fav, address } = positionData;
    const requirement = positionData?.requirement?.split(/[\r\n]/);
    const description = positionData?.description?.split(/[\r\n]/);
    const galleries = positionData?.galleries?.length ? positionData?.galleries : client?.galleries;

    const onChangeFavs = () => {
      if (!fav?.id) {
        container.saasService.addPositionFavs(id).then((res: any) => {
          setPositionData({ ...positionData, fav: { id: res.id } });
        });
      } else {
        container.saasService.cancelPositionFavs(fav.id).then(() => {
          setPositionData({ ...positionData, fav: null });
        });
      }
    };

    const pageStyle: React.CSSProperties = {};
    if (mode === 'list') {
      pageStyle.paddingTop = navbarHeight;
    }

    return (
      <View className="container container-position-detail">
        <View className="position-detail-container" style={pageStyle}>
          <CustomNav
            showFavIcon
            showShareIcon={false}
            fav={fav}
            onChangeFavs={onChangeFavs}
            onChangeShareVisible={() => {
              dispatch(setShareData({
                position: positionData,
                params: shareParams,
                visible: true,
              }));
              // setShareVisible(true);
            }}
          />

          {mode === 'card' && <View style={{ position: 'relative' }}>
            <SwiperGallery galleries={galleries as any} />
            <PositionTrack trackData={trackData} />
          </View>}

          <PositionHeader positionData={positionData} trackData={trackData} />

          {!!advantages?.length && <Panel title="职位福利">
            {advantages.map((item) => (
              <Tag key={item} color="gray">
                {item}
              </Tag>
            ))}
          </Panel>}

          {!!positionData?.requirement && <Panel title="职位要求">
            {requirement.map(item => (
              <View key={item} className="position-panel__desc">
                {item}
              </View>
            ))}
          </Panel>}
          <Panel title="职位描述">
            {description.map(item => (
              <View key={item} className="position-panel__desc">
                {item}
              </View>
            ))}
          </Panel>

          {address && <Panel title="工作地址">
            <AddressNavigation address={address} />
          </Panel>}

          <PositionInterviewer positionData={positionData} />

          {mode === 'list' && <ScrollGallery data={positionData.galleries} title="工作环境" />}

          <ClientCard data={positionData} />

          <SharePageContainer onChangeShareImg={(url) => {
            setImageUrl(url);
          }} onCancel={() => {
            dispatch(setShareData({
              visible: false,
            }));
          }} visible={shareData?.visible}
          />
          <Footer className="position-details__footer">
            <Block>
              <ShareAndFav
                interviewer={interviewer}
                fav={fav}
                onChangeFavs={onChangeFavs}
                onChangeShareVisible={() => {
                  dispatch(setShareData({
                    position: positionData,
                    params: shareParams,
                    visible: true,
                  }));
                  // setShareVisible(true);
                }}
              />
              <QzButton
                className="position-details__chat"
                openType="contact"
                sessionFrom={`#${positionData?.id}${positionData?.name}（${positionData?.client?.name}）`}
                customStyle={['mt', 'verdant'].includes(themeValue) ? {
                  border: 'solid 2px #000',
                  backgroundColor: '#13CE66',
                } : {
                  border: `solid 1px #13CE66`,
                  backgroundColor: '#13CE66',
                }}
                circle
                fluid
                type="primary"
                // onClick={chat}
              >
                在线咨询
              </QzButton>
              {flow?.id ? (
                <QzButton
                  className="position-details__submit"
                  circle
                  fluid
                  onClick={() => {
                    Taro.redirectTo({
                      url: `/pages/flow/list`,
                    });
                  }}
                  type="primary"
                >
                  查看报名进展
                </QzButton>
              ) : (
                <DeliverButton
                  className="position-details__submit"
                  id={id}
                  type="primary"
                  onSuccess={() => {
                    updateFlowStatus();
                  }}
                />
              )}
            </Block>
          </Footer>
        </View>
      </View>
    );
  } else {
    return null;
  }
};
export default PositionDetail;
