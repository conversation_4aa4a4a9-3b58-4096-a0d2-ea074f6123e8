import { View } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { TEMPLATE_INFO } from '@utils/data'
import { mergeTplConfig } from '@utils/index'
import type { FC } from 'react'
import './index.scss'

type TemplateListProps = {}
const TemplateList: FC<TemplateListProps> = () => {
  const { TPL_CONFIG } = Taro.getApp().$app;
  const switchTemplate = (value) =>{
    mergeTplConfig(value,TPL_CONFIG)
    Taro.reLaunch({ url: `/pages/index/index` });
  }

  Taro.loadFontFace({
    family: 'jzjdmhj',
    source: 'url("https://candidate.lingzhao.net/JiZiJingDianMeiHeiJian.ttf")',
    success: ()=>{
      console.log('font load success')
    }
  })
  const style = {
    fontFamily: 'jzjdmhj',
  }

  return (
    <View className="qz-templateList">
      {/* <View className="qz-templateList__title">选择风格</View> */}
      <View className="qz-templateList__content">
        {TEMPLATE_INFO.map((item)=>{
          return <View className="qz-templateList__content-create item"  onClick={(): void => {
            switchTemplate(item.value);
          }}>
          <View className="qz-templateList__content-image" style={{
            border:`solid 4px ${item.color}`,
            color: item.color,
            ...style
          }}>
            {item.alias}
          </View>
          <View
            className="qz-templateList__content-wrap"
            
          >
            <View className="qz-templateList__content-title">
              <View className="qz-templateList__content-text">
                {item.title}
              </View>
            </View>
            <View className="qz-templateList__content-subtitle">
              {item.desc}
            </View>
          </View>
        </View>
        })}
        
      </View>
     </View>
  )
}
export default TemplateList