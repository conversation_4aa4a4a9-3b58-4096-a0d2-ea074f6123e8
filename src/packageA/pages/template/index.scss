.qz-templateList{
  margin-top: 20px;
  &__title{
    font-size:20px;
    text-align: center;
    padding:30px 0;
  }
  &__content{
    padding: 0 16px;
    &-image{
      text-align: center;
      line-height: 48px;
      font-size: 32px;
      height: 48px;
      width: 80px;
      display: inline-block;
      letter-spacing: -4px;
    }
    &-text{
      padding-right: 8px;
    }
    &-title{
      display: flex;
      align-items: center;
    }
    &-subtitle{
      padding-top: 6px;
      font-size: 12px;
      line-height: 20px;
      color: #888;
    }
    .item{
      display: flex;
      align-items: center;
      position: relative;
      padding-left: 16px;
      gap:16px;
      width:100%;
      height: 70px;
      box-shadow: 0px 5px 15px rgba(0,0,0, .1);
      border-radius: 8px;
      margin-bottom: 16px;
      background-color: #fff;
    }

  }
}