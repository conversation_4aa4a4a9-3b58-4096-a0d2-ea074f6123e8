import { View } from '@tarojs/components';
import type { FC } from 'react';
import './index.scss';

type PanelProps = {
  title?: string;
  children: React.ReactNode;
  style?: Record<string, any>;
};
const Panel: FC<PanelProps> = ({ title, children, style }) => {
  const bodyStyle:React.CSSProperties = {};
  if(!title) bodyStyle.marginTop = 0;
  return (
    <View className="qz-Panel" style={style}>
      {title ? <View className="qz-Panel__title">{title}</View> : null}
      <View className="qz-Panel__content" style={bodyStyle}>{children}</View>
    </View>
  );
};
export default Panel;
