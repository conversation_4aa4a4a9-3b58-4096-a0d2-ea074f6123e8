import { View } from '@tarojs/components'
import './no-more.scss'

type Props = {
  children: import('react').ReactChild,
  style?: React.CSSProperties
}

/**
 * 薪资展示.
 * @param {array} tags
 */
const NoMore: FixFunctionComponent<Props, 'defaultProps'> = (props: Props) => {
  const { children,style } = props

  return <View className="no-more" style={style}>{children}</View>
}
NoMore.defaultProps = {
  children: '没有更多数据了~'
}

export default NoMore
