import { View } from '@tarojs/components'
import type { FC } from 'react'
import './index.scss'

type CardFooterProps = {
  desc?: React.ReactNode
  action: React.ReactNode[]
}
const CardFooter: FC<CardFooterProps> = ({desc,action}) => {
  return (
    <View className="qz-CardFooter">
      <View className="qz-CardFooter__desc">{desc}</View>
      <View className="qz-CardFooter__btns">
        {action.map((item,index)=>{
          return <View key={index}>{item}</View>
        })}
      </View>
    </View>
  )
}
export default CardFooter