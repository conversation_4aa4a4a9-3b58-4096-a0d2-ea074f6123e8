import Panel from '@components/Panel'
import { Image, ScrollView, Video, View } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { formatOssUrl } from '@utils/format'
import { getFileSuffix } from '@utils/index'
import type { FC } from 'react'
import './index.scss'

type ScrollGalleryProps = {
  data?: {name:string,url:string}[]
  title?: string
}
const ScrollGallery: FC<ScrollGalleryProps> = ({
  data: galleries,
  title = '公司环境',
}) => {
  if (!galleries?.length) return null
  const sources:any[] = galleries.map((item)=>{
    const { url } = item
    const suffix = getFileSuffix(url)
    const isVideo = ['mp4', 'avi'].includes(suffix)
    return {
      url,
      type: isVideo ? 'video':'image'
    }
  })
  
  return (
    <Panel title={title}>
      <View className="qz-ScrollGallery">
        <ScrollView
          scrollX
          scrollWithAnimation
          className="qz-ScrollGallery__scrollView">
          <View className="qz-ScrollGallery__content">
            {sources.map((item,index) => {
              const {type} = item;
                return (
                  <View key={item.name} onClick={()=>{
                    Taro.previewMedia({
                      current: index,
                      sources // 需要预览的图片http链接列表
                    })
                  }}>
                    {type === 'video' ? <Video
                      style="border-radius: 10px;overflow:hidden"
                      objectFit="fill"
                      src={item.url}
                      // poster='https://misc.aotu.io/booxood/mobile-video/cover_900x500.jpg'
                      initialTime={0}
                      controls={true}
                      autoplay={false}
                      loop={false}
                      muted={false}
                    />:<Image
                    mode="aspectFill"
                    style="height: 225px;background: #000;border-radius: 10px;overflow:hidden"
                    src={formatOssUrl(item.url)}
                  />}
                  </View>
                )
             
            })}
          </View>
        </ScrollView>
      </View>
    </Panel>
  )
}
export default ScrollGallery
