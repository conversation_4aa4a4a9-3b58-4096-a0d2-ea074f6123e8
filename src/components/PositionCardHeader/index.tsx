import { Text, View } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { getClientDisplayName } from '@utils/index'
import type { FC } from 'react'
import { useDispatch } from "react-redux";
import { setPositionData } from '@stores/actions/position';
import './index.scss'

type PositionCardHeaderProps = {
  positionData: PositionTypes
}
const PositionCardHeader: FC<PositionCardHeaderProps> = ({positionData}) => {

  const dispatch = useDispatch()
  const {name,id,isHot,isTop} = positionData;
  const handleNavigateTo = (url: string) => {
    dispatch(setPositionData(positionData))
    Taro.navigateTo({
      url
    });
  }
  return (
    <View className="qz-PositionCardHeader" onClick={() =>
      handleNavigateTo(`/packageA/pages/position/detail?id=${id}`)
    }>
      <View className="qz-PositionCardHeader__nameWrap">
        {!!(isHot || isTop) && <View className="qz-PositionCardHeader__iconWrap">
        {!!isTop && <View className="qz-PositionCardHeader__hotAndTop" style={{backgroundColor:'#0066ff'}}>急</View>}
        {!!isHot && <View className="qz-PositionCardHeader__hotAndTop" style={{backgroundColor:'#FE3926'}}>热</View>}
        </View>}

        <Text className="qz-PositionCardHeader__position">{name}</Text>
      </View>

      <Text className="qz-PositionCardHeader__client">{getClientDisplayName(positionData)}</Text>
    </View>
  )
}
export default PositionCardHeader
