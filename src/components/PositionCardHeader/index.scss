.qz-PositionCardHeader {
  // padding-right: 15px;
  display: flex;
  flex-wrap: wrap;
  align-items: baseline;
  &__nameWrap{
    display: flex;
    align-items: flex-start;
    gap:4px;
  }
  &__iconWrap{
    display: flex;
    gap: 4px;
    padding-top: 4px;
  }
  &__hotAndTop {
    line-height: 1;
    padding:1px 2px;
    border-radius: 2px;
    font-size: 12px;  
    color: #fff;
  }
  &__position {
    padding-right: 6px;
    font-size: 16px;
    color: #000;
    font-weight: 500;
    line-height: 22px;
  }
  &__client {
    font-size: 12px;
    color: #666;
    line-height: 16px;
  }
}