import { View } from '@tarojs/components'
import { getResumeRequirements } from '@utils/index'
import type { FC } from 'react'
import { useSelector } from 'react-redux'
import './index.scss'

type ResumeRequirementsProps = {
  resume:ResumeTypes
}
const ResumeRequirements: FC<ResumeRequirementsProps> = ({resume}) => {
  if(!resume) return null;
  const {dictMap={}} = useSelector((state:any) => state.dict);
  const info = getResumeRequirements(resume,dictMap);

  return (
    <View className="qz-ResumeRequirements">
      {info.map(item => (
        <View key="item" className="qz-ResumeRequirements__item">
          {item}
        </View>
      ))}
    </View>
  )
}
export default ResumeRequirements
