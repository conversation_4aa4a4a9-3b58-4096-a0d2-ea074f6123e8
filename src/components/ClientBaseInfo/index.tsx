import { View } from '@tarojs/components';
import type { FC } from 'react'
import { useSelector } from 'react-redux';

type ClientBaseInfoProps = {
  data: Partial<ClientTypes>;
}
const ClientBaseInfo: FC<ClientBaseInfoProps> = ({data}) => {
  const {dictMap={}} = useSelector((state:any) => state.dict);
  const scaleMap = dictMap?.client?.scale || {};
  const industryMap = dictMap?.common?.industry || {};
  const { scale, industry } = data;

  if(!scale && !industry?.length){
    return null;
  }

  const getClientBaseInfo = () => {
    const info:string[] = [];
    if (scale && scaleMap[scale]) {
      info.push(scaleMap[scale]);
    }
    if (industry?.length && industryMap[industry[0]]) {
      info.push(industryMap[industry[0]].name);
    }

    if (info.length) {
      return info.join(" · ");
    } else {
      return null;
    }
  };

  return (
    <View className="qz-ClientBaseInfo">{getClientBaseInfo()}</View>
  )
}
export default ClientBaseInfo