import IconFont from '@components/iconfont';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import type { FC } from 'react';
import { useDispatch } from "react-redux"
import { setResumeData } from '@stores/actions/resume'
import './index.scss';

type ResumeNameEditProps = {
  resume: ResumeTypes
}
const ResumeNameEdit: FC<ResumeNameEditProps> = ({resume}) => {
  const {name} = resume;
  const dispatch = useDispatch()
  return (
    <View className="qz-ResumeNameEdit">
      <View className="qz-ResumeNameEdit__name">{name}</View>
      <View
        onClick={() => {
          dispatch(setResumeData(resume))
          Taro.navigateTo({
            url: `/packageA/pages/resume/edit?type=basic`,
          });
        }}
      >
        <IconFont size={18} color="#0066ff" name="bianji" />
      </View>
    </View>
  )
}
export default ResumeNameEdit
