import React from "react";
import { View } from '@tarojs/components'
import classNames from 'classnames'
import _isFunction from 'lodash/isFunction'
import './index.scss'

type IProps = {
  position?: 'left'|'bottom';
  isOpened: boolean;
  isShowClose?: boolean;
  children: import("react").ReactChild;
  className?: string;
  onClose?: any;
}
type IState = {
  _isOpened: boolean
  _isShowClose: boolean
  position?: 'left'|'bottom';
}
export default class Popup extends React.Component {
  props: IProps
  state: IState

  constructor(props: IProps) {
    super(props)
    const { isOpened,isShowClose=true,position } = props
    this.state = {
      _isOpened: isOpened,
      position: position || 'bottom',
      _isShowClose: isShowClose
    }
  }

  componentWillReceiveProps(nextProps): void {
    const { isOpened } = nextProps

    if (isOpened !== this.state._isOpened) {
      this.setState({
        _isOpened: isOpened
      })
    }
  }

  handleClose = (): void => {
    this.setState(
      {
        _isOpened: false
      },
      this.handleParentClose
    )
  }

  handleParentClose = (): void => {
    if (_isFunction(this.props.onClose)) {
      this.props.onClose()
    }
  }

  render(): JSX.Element {
    const { _isOpened,_isShowClose,position } = this.state
    const { children } = this.props
   const rootClass = classNames(`at-float-layout at-float-layout--${position}`, {
    'at-float-layout--active': _isOpened,
  }, this.props.className);
    return (
      <View className={rootClass}>
      <View className="at-float-layout__overlay" onClick={this.handleClose}></View>
      <View className="at-float-layout__container layout">
        {_isShowClose  && <View className="layout-header__btn-close" onClick={this.handleClose}></View>}
        <View>{children}</View>
      </View>
    </View>
    )
  }
}

