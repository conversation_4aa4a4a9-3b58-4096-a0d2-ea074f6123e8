
.at-float-layout {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  visibility: hidden;
  z-index: 9999;
  -webkit-transition: visibility 600ms cubic-bezier(0.36, 0.66, 0.04, 1);
  -o-transition: visibility 600ms cubic-bezier(0.36, 0.66, 0.04, 1);
  transition: visibility 600ms cubic-bezier(0.36, 0.66, 0.04, 1);
  /* elements */
  /* modifiers */
  &--left{
    .at-float-layout__container{
      min-width: 40px;
      height: 100vh;
      -webkit-transform: translate3d(-100%, 0, 0);
              transform: translate3d(-100%, 0, 0);
    }
  }
  &--bottom{
    .at-float-layout__container{
      width: 100%;
      max-height: 90vh;
      min-height: 400px;
      -webkit-transform: translate3d(0, 100%, 0);
              transform: translate3d(0, 100%, 0);
    }
  }
}
.at-float-layout__overlay {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
  opacity: 0;
  -webkit-transition: opacity 150ms ease-in;
  -o-transition: opacity 150ms ease-in;
  transition: opacity 150ms ease-in;
}
.at-float-layout__container {
  position: absolute;
  bottom: 0;
  overflow-y:auto;
  background-color: #FFF;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  -webkit-transition: -webkit-transform 300ms cubic-bezier(0.36, 0.66, 0.04, 1);
  transition: -webkit-transform 300ms cubic-bezier(0.36, 0.66, 0.04, 1);
  -o-transition: transform 300ms cubic-bezier(0.36, 0.66, 0.04, 1);
  transition: transform 300ms cubic-bezier(0.36, 0.66, 0.04, 1);
  transition: transform 300ms cubic-bezier(0.36, 0.66, 0.04, 1), -webkit-transform 300ms cubic-bezier(0.36, 0.66, 0.04, 1);
}
// .at-float-layout__container {
//   position: absolute;
//   bottom: 0;
//   width: 100%;
//   max-height: 600px;
//   min-height: 400px;
//   overflow-y:auto;
//   background-color: #FFF;
//   border-top-left-radius: 10px;
//   border-top-right-radius: 10px;
//   -webkit-transform: translate3d(0, 100%, 0);
//           transform: translate3d(0, 100%, 0);
//   -webkit-transition: -webkit-transform 300ms cubic-bezier(0.36, 0.66, 0.04, 1);
//   transition: -webkit-transform 300ms cubic-bezier(0.36, 0.66, 0.04, 1);
//   -o-transition: transform 300ms cubic-bezier(0.36, 0.66, 0.04, 1);
//   transition: transform 300ms cubic-bezier(0.36, 0.66, 0.04, 1);
//   transition: transform 300ms cubic-bezier(0.36, 0.66, 0.04, 1), -webkit-transform 300ms cubic-bezier(0.36, 0.66, 0.04, 1);
// }
.at-float-layout .layout-header {
  position: relative;
}
.at-float-layout .layout-header__title {
  padding-top: 50px;
  overflow: hidden;
  -o-text-overflow: ellipsis;
     text-overflow: ellipsis;
  white-space: nowrap;
  color: #333;
  font-size: 24px;
  display: block;
  text-align: center;
}
.at-float-layout .layout-header__btn-close {
  position: absolute;
  padding: 25px;
  top: 0px;
  right: 0px;
  line-height: 1;
}
.at-float-layout .layout-header__btn-close::before, .at-float-layout .layout-header__btn-close::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  display: inline-block;
  width: 18px;
  height: 2PX;
  border-radius: 1PX;
  background: #CCC;
}
.at-float-layout .layout-header__btn-close::before {
  -webkit-transform: translate3d(-50%, -50%, 0) rotate(45deg);
          transform: translate3d(-50%, -50%, 0) rotate(45deg);
}
.at-float-layout .layout-header__btn-close::after {
  -webkit-transform: translate3d(-50%, -50%, 0) rotate(-45deg);
          transform: translate3d(-50%, -50%, 0) rotate(-45deg);
}
.at-float-layout .layout-body {
  font-size: 14px;
  padding: 8px;
  height: 300px;
}
.at-float-layout--active {
  visibility: visible;
}
.at-float-layout--active .at-float-layout__overlay {
  opacity: 1;
}
.at-float-layout--active .at-float-layout__container {
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
}
