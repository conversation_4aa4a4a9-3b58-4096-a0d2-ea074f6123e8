import { FC } from "react";
import Taro from "@tarojs/taro";
import { View } from "@tarojs/components";
import QzIcon from "@components/Icon";
import QzButton from "@components/Button";
import { getClientDisplayName, getPositionBaseInfo } from "@utils/index";
import Tag from "@components/Tag";
import "./index.scss";
import { useSelector } from "react-redux";
import PositionSalary from "@components/PositionSalary";

type Props = {
  style?: React.CSSProperties;
  positionData: PositionTypes;
  onCancelFav?: () => void;
};
/**
 * 职位卡片
 * @param props
 */
const FavPositionCard: FC<Props> = ({ style, positionData, onCancelFav }) => {
  const {
    id,
    name,
    city,
    workType,
    degree,
    workYears,
    advantages
  } = positionData;

  const {dictMap={}} = useSelector((state:any) => state.dict);
  const baseInfo = getPositionBaseInfo(
    { city, workType, workYears, degree },
    dictMap
  );

  function handleNavigateTo(url: string): void {
    Taro.navigateTo({
      url
    });
  }

  const tags = [...baseInfo, ...advantages];
  return (
    <View
      style={style}
      className="qz-FavPositionCard"
      onClick={() => handleNavigateTo(`/packageA/pages/position/detail?id=${id}`)}
    >
      <View className="qz-FavPositionCard__hd">
        <View className="qz-FavPositionCard__title">{name}</View>
        <QzButton
          className="qz-FavPositionCard__btn"
          circle
          fluid
          onClick={()=>{
            onCancelFav && onCancelFav();
          }}
          type="gray"
        >
          取消收藏
        </QzButton>
      </View>

      <PositionSalary
        className="qz-FavPositionCard__salary"
        positionData={positionData}
      />

      <View className="qz-FavPositionCard__company">
        <QzIcon
          className="qz-FavPositionCard__company-icon"
          size="13"
          type="icon_company"
        />
        {getClientDisplayName(positionData)}
      </View>
      <View className="qz-FavPositionCard__tags">
        <View>
          {tags.slice(0, 5).map(item => {
            return (
              <Tag key={item?.label || item} color="gray">
                {item?.value || item}
              </Tag>
            );
          })}
          {tags?.length > 5 && <Tag color="gray">···</Tag>}
        </View>
      </View>
    </View>
  );
};

export default FavPositionCard;
