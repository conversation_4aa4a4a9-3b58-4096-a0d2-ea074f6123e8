@import '@styles/variables/default.scss';
@import '@styles/mixins/index.scss';
.qz-FavPositionCard {
  background-color: #fff;
  overflow: hidden;
  padding: 15px;
  margin-bottom: 10px;
  position: relative;
  &__salary{
    margin-top: 10px;
  }
  &__hd {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  &__title {
    font-size: 16px;
    color: #262626;
    flex: 1;
    font-weight: 500;
    line-height: 20px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  // &__salary {
  //   margin-top: 10px;
  // }

  &__company {
    position: relative;
    margin-top: 10px;
    font-size: 12px;
    // color: #262626;
    color: #656565;
    padding-left: 18px;
    line-height: 12px;

    &-icon {
      @include absolute-center(absolute, vertical);
      left: 0;
    }
  }

  &__tags {
    margin-top: 10px;
    @include display-flex();
    @include align-items(center);
  }
  &__tag {
    margin-right: 5px;
  }

 

  &__btn {
    width: 96px !important;
    padding: 0 !important;
  }

}
