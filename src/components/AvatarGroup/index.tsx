import { Image, View } from "@tarojs/components";
import { FC } from "react";
import "./index.scss";

type AvatarGroupProps = {
  data: string[];
};
const defaultAvatarUrl = require('../Icon/images/unknow.jpeg');
const defaultAvatars = [
  require('../Icon/images/man1.png'),
  require('../Icon/images/women1.png'),
  require('../Icon/images/man2.png'),
  require('../Icon/images/women2.png'),
];
const max = 4;
const AvatarGroup: FC<AvatarGroupProps> = ({ data }) => {

  // 处理空头像
  let index = 0;
  const newData = data.map((item)=>{
    if(!item){
      item = defaultAvatars[index];
      if(index === defaultAvatars.length-1){
        index = 0;
        return defaultAvatars[defaultAvatars.length-1]
      }else{
        index++;
        return defaultAvatars[index-1]
      }
    }else{
      return item;
    }
  })
  const avatars = [...defaultAvatars, ...newData];
  
  
  let newAvatars:any[] = [];
  // if(avatars.length <= max){
  //   newAvatars = avatars;
  // }
  // if(avatars.length > max){
    newAvatars = avatars.slice(avatars.length - max)
  // }
  return (
    <View className="qz-AvatarGroup">
      {newAvatars.map((item,index) => {
        return (
          <Image className="qz-AvatarGroup__item" style={{
            width: `20px`,
            height: `20px`,
            borderRadius: '20px',
            backgroundColor: '#f5f5f5'
          }} key={item+index} src={item} onError={(e)=>{
            // todo: 无效
            e.target.src = defaultAvatarUrl
          }} />
        );
      })}
    </View>
  );
};
export default AvatarGroup;
