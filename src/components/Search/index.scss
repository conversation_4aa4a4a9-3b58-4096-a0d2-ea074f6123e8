@import '@styles/variables/default.scss';
.search{
  // position: fixed;
  width: 100%;
  height: 60px;
  top:0;
  background-color: #fff;
  display: flex;
  align-items: flex-end;
  padding: 0 15px;
  
  box-sizing: border-box;
  // justify-content: center;
  z-index: 9;
  &-content {
    position: relative;
    background: rgba(247,247,247,1);
    border-radius: 40px;
    margin-bottom: 10px;
    width: 100%;
    height: 40px;
    display: flex;
    &__icon{
      position: absolute;
      left:15px;
      top:50%;
      transform: translateY(-50%);
    }
    &__input {
      height: 100%;
      padding: 0 20px 0 40px;
      font-weight: 400;
      font-size: 16px;
      color: #111;
      flex: 1;
      &-placeholder {
        font-size: 14px;
        color:rgba(187,187,187,1);
      }
    }
  }
}
