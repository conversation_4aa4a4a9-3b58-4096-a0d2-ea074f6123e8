import type { FC } from 'react'
import { View } from "@tarojs/components";
import IconFont from '@components/iconfont';
import "./index.scss";

type SearchProps = {
  style?:React.CSSProperties
}
const Search: FC<SearchProps> = ({style,children}) => {
  return (
    <View className="search" style={style}>
      <View className="search-content">
        <View className="search-content__icon"><IconFont  name="mp-search" color={'#999999'} size={16} /></View>
        {children}
      </View>
    </View>
  );
};

export default Search;
