import { View } from '@tarojs/components'
import type { FC } from 'react'
import './index.scss'

type PositionCoverImgProps = {
  coverImg: string
}
const PositionCoverImg: FC<PositionCoverImgProps> = ({coverImg}) => {
  return (
    <View
        className="qz-PositionCoverImg"
        style={{
          backgroundImage: `url(${coverImg})`,
        }}
      ></View>
  )
}
export default PositionCoverImg