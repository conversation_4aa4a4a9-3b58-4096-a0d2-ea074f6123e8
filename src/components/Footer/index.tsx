import { View } from '@tarojs/components'
import classNames from 'classnames'
import './index.scss'

type Props = {
  children: import('react').ReactChild
  className?: string
}

/**
 * footer.
 * @param {array} tags
 */
function Footer(props: Props): JSX.Element {
  const { children } = props

  return (
    <View className="footer">
      <View className={classNames(props.className, 'footer-inner')}>{children}</View>
    </View>
  )
}

export default Footer
