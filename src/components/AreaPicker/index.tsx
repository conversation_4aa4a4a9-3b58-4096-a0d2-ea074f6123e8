import IconFont from "@components/iconfont";
import { Picker, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { FC, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import "./index.scss";

type AreaPickerProps = {
  filterValues: any;
  onChangeFilterValues?: (values: any) => void;
};
const AreaPicker: FC<AreaPickerProps> = ({
  filterValues,
  onChangeFilterValues
}) => {
  const { TPL_CONFIG } = Taro.getApp().$app;
  const recruitment = TPL_CONFIG?.recruitment;
  const { dictMap = {}, dictArr = {} } = useSelector(
    (state: any) => state.dict
  );
  const cityMap = dictMap?.common?.city;
  const cityArr = dictArr?.common?.city;
  const [cityPickerOptions, setCityPickerOptions] = useState<any>([]);
  const [value, setValue] = useState<any[]>([0, 0]);

  const getCityPickerOptions = () => {
    const provinceDictData = cityArr.filter(
      item => item.id === recruitment.province
    );
    const currentCity = [{ id: 0, name: "不限" }];
    provinceDictData[0].children.forEach(item => {
      if (recruitment.city.includes(item.id)) {
        currentCity.push({
          id: item.id,
          name: item.name,
          children: item.children
        });
      }
    });
    return currentCity;
  };

  useEffect(() => {
    if (recruitment?.city) {
      const newCityPickerOptions = getCityPickerOptions();
      setCityPickerOptions([newCityPickerOptions, []]);
    }
  }, []);
  return (
    <View className="qz-AreaPicker">
    <Picker
      rangeKey="name"
      mode="multiSelector"
      range={cityPickerOptions}
      value={value}
      onChange={e => {
        const [firstCol, secondCol] = e.detail.value;
        setValue([firstCol, secondCol]);
        onChangeFilterValues &&
          onChangeFilterValues({
            ...filterValues,
            city:
              firstCol === 0 ? undefined : cityPickerOptions[1][secondCol].id
          });
      }}
      onColumnChange={e => {
        const { column, value } = e.detail;
        if (column === 0) {
          if (value === 0) {
            setCityPickerOptions([cityPickerOptions[0], []]);
            setValue([0, 0]);
          } else {
            setCityPickerOptions([
              cityPickerOptions[0],
              cityPickerOptions[0][value].children
            ]);
            setValue([value, 0]);
          }
        }
      }}
    >
      <View className="qz-AreaPicker__value">
        {/* <IconFont name="city" size={14} color="666" /> */}
        <View className="qz-AreaPicker__value-text">
          {filterValues.city ? cityMap[filterValues.city].name : "工作区域"}
        </View>
        <IconFont name="arrowdownb" size={14} color="#999" />
      </View>
    </Picker>
    </View>
  );
};
export default AreaPicker;
