import IconFont from '@components/iconfont'
import { View } from '@tarojs/components'
import type { FC } from 'react'
import { ui } from '@landrover/business/index'
import './index.scss'
import QzButton from '@components/Button'
import Taro from '@tarojs/taro'

type ShareAndFavProps = {
  interviewer: {
    name: string
    phone: string
  }|null;
  fav:{id:number} | null;
  onChangeShareVisible: () => void;
  onChangeFavs: ()=>void;
}
const ShareAndFav: FC<ShareAndFavProps> = ({
  interviewer,
  fav,
  onChangeShareVisible,
  onChangeFavs
}) => {
  return (
    <View className="qz-ShareAndFav">
      <View className="qz-ShareAndFav__item" onClick={()=>{
        onChangeShareVisible && onChangeShareVisible();
      }}>
        <IconFont name="fenxiang7" color={"#333"} size={20} />
        <View>分享</View>
      </View>
      {/* <View className="qz-ShareAndFav__item" onClick={()=>{
        onChangeFavs && onChangeFavs();
        ui.showToast(fav?.id?'取消收藏':'收藏成功！可在个人中心查看');
      }}>
        <IconFont name="like" color={fav?.id? '#FE3925':'#000000'} size={22} />
        <View>收藏</View>
      </View> */}
      {/* <View className="qz-ShareAndFav__item">
        <IconFont name="xiaoxi" color={"#00B96B"} size={18} />
        <View>在线问</View>
        <QzButton
          openType="contact"
          type="primary"
          className="qz-button__transparent"
          // onClick={chat}
        >
        在线咨询
        </QzButton>
      </View> */}
      {!!interviewer && <View className="qz-ShareAndFav__item" onClick={()=>{
        Taro.makePhoneCall({
          phoneNumber: interviewer.phone //仅为示例，并非真实的电话号码
        })
      }}>
        <IconFont name="tel1" color={"#333"} size={20} />
        <View>打电话</View>
      </View>}
      
      
    </View>
  )
}
export default ShareAndFav