import IconFont from '@components/iconfont';
import { Picker, View } from '@tarojs/components';
import { FC, useState } from 'react'
import './index.scss'

type SalaryPickerProps = {
  filterValues: any
  onChangeFilterValues: (values:any)=>void
}
const SalaryPicker: FC<SalaryPickerProps> = ({filterValues,onChangeFilterValues}) => {
  const [salaryValue, setSalaryValue] = useState<any[]>([0]);
  const salaryOptions = [
    { id: "0", name: "不限" },
    { id: "0,1000", name: "1000元以下" },
    { id: "1000,2000", name: "1000-2000元" },
    { id: "2000,3000", name: "2000-3000元" },
    { id: "3000,5000", name: "3000-5000元" },
    { id: "5000,8000", name: "5000-8000元" },
    { id: "8000,12000", name: "8000-12000元" },
    { id: "12000,20000", name: "12000-20000元" },
    { id: "20000,25000", name: "20000-25000元" },
    { id: "25000", name: "25000元以上" }
  ];
  return (
    <View className="qz-SalaryPicker">
      <Picker
        mode="selector"
        rangeKey="name"
        range={salaryOptions}
        value={salaryValue}
        onChange={e => {
          const { value } = e.detail;
          onChangeFilterValues &&
            onChangeFilterValues({
              ...filterValues,
              salary:
                salaryOptions[value].id === "0"
                  ? undefined
                  : salaryOptions[value].id
            });
          setSalaryValue([value]);
        }}
      >
        <View className="qz-SalaryPicker__value">
          <View className="qz-SalaryPicker__value-text">
          {salaryValue[0] && salaryValue[0] !== "0"
            ? salaryOptions[salaryValue[0]]?.name
            : "薪资范围"}
          </View>
          <IconFont name="arrowdownb" size={14} color="#999" />
        </View>
      </Picker>
    </View>
  )
}
export default SalaryPicker