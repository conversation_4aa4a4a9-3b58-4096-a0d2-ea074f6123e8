@import '@styles/variables/default.scss';
@import '@styles/mixins/index.scss';

.work-tags {
  @include display-flex();
  @include align-items(center);

  &__item {
    position: relative;
    color: #2B2B2B;
    font-size: 14px;
    padding-right: 10px;
    margin-right: 10px;

    &::after {
      content: '';
      @include absolute-center(absolute, vertical);
      right: -2px;
      width: 4px;
      height: 4px;
      border-radius: 100%;
      background-color: #2B2B2B;
    }
    &:last-child::after {
      display: none;
    }
  }
}
