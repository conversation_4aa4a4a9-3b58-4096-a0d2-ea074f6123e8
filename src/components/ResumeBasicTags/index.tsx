import Taro from '@tarojs/taro'
import { View } from '@tarojs/components'
import classNames from 'classnames'
import './work-tags.scss'

type Props = {
  tags: string[]
  className: string
}

/**
 * 薪资展示.
 * @param {array} tags
 */
const ResumeBasicTags: FixFunctionComponent<Props, 'defaultProps'> = (
  props: Props
) => {
  const { tags,className } = props

  return (
    <View  className={classNames(className, 'work-tags')}>
      {tags.map(
        tag =>
          tag && (
            <View key={tag} className="work-tags__item">
              {tag}
            </View>
          )
      )}
    </View>
  )
}
ResumeBasicTags.defaultProps = {
  tags: ['30', '工作八年', '本科']
}

export default ResumeBasicTags
