import { View } from '@tarojs/components';
import type { FC } from 'react';
import './index.less';
import { getPositionBaseInfo } from '@utils/index';
import { useSelector } from 'react-redux';

type PositionBaseInfoProps = {
  position: PositionTypes;
};
const PositionBaseInfo: FC<PositionBaseInfoProps> = ({ position }) => {
  const {dictMap={}} = useSelector((state:any) => state.dict);
  const { number, degree, workYears, city } = position;
  const info = getPositionBaseInfo({ city, workYears, degree, number }, dictMap);
  return (
    <View className="qz-PositionBaseInfo">
      {info.map((item) => (
        <View key={item.label} className="qz-PositionBaseInfo__item">
          {item.value}
        </View>
      ))}
    </View>
  );
};
export default PositionBaseInfo;
