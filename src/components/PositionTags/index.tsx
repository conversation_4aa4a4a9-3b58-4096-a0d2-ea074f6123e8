import { View } from '@tarojs/components';
import { getPositionBaseInfo } from '@utils/index';
import type { FC } from 'react'
import { useSelector } from 'react-redux';
import Tag from "@components/Tag";

type PositionTagsProps = {
  positionData: PositionTypes
}
const PositionTags: FC<PositionTagsProps> = ({positionData}) => {

  const {
    city,
    degree,
    workYears,
    advantages,
  } = positionData;

  const {dictMap={}} = useSelector((state:any) => state.dict);
  const baseInfo = getPositionBaseInfo({ city, workYears, degree }, dictMap);
  const tags = [...baseInfo, ...advantages];

  return (
    <View className="position-card__tags">
      <View style={{
        display:'flex',
        flexWrap:'nowrap'
      }}>
        {tags.slice(0, 5).map(item => {
          return (
            <Tag key={item?.label || item} color="gray">
              {item?.value || item}
            </Tag>
          );
        })}
        {tags?.length > 5 && <Tag color="gray">···</Tag>}
      </View>
    </View>
  )
}
export default PositionTags