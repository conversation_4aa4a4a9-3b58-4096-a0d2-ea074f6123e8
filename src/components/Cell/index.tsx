import { View } from '@tarojs/components';
import type { FC } from 'react';
import './index.scss';

type CellProps = {
  label: string;
  children: React.ReactNode;
};
const Cell: FC<CellProps> = ({ label, children }) => {
  return (
    <View className="qz-Cell">
        <View className="qz-Cell__label">{label}：</View>
        <View className="qz-Cell__value">{children}</View>
    </View>
  );
};
export default Cell;
