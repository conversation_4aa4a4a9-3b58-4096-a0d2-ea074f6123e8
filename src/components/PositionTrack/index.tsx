import AvatarGroup from '@components/AvatarGroup';
import { View } from '@tarojs/components';
import type { FC } from 'react'
import './index.scss'

type PositionTrackProps = {
  trackData:any
}
const PositionTrack: FC<PositionTrackProps> = ({trackData}) => {
  const {uv=0,lastVisitorAvatars=[]} = trackData || {};
  return (
    <View className='qz-PositionTrack'>
      <AvatarGroup data={lastVisitorAvatars} />
      <View>{uv+4}人浏览</View>
    </View>
  )
}
export default PositionTrack