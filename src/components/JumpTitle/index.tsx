import ArrowRightIcon from '@components/ArrowRightIcon'
import { View } from '@tarojs/components'
import type { FC } from 'react'
import './index.scss'

type JumpTitleProps = {
  iconShow?: boolean
  title: React.ReactNode
  onClick?: ()=>void;
}
const JumpTitle: FC<JumpTitleProps> = ({iconShow=true,title,onClick}) => {
  return (
    <View className="qz-JumpTitle" onClick={()=>{
      onClick && onClick()
    }}>
      <View className="qz-JumpTitle__title">{title}</View>
      {iconShow && <ArrowRightIcon />}
    </View>
  )
}
export default JumpTitle