import { View } from '@tarojs/components'
import type { FC } from 'react'
import './index.scss'

type ListItemProps = {
  label?:string
  required?: boolean
}
const ListItem: FC<ListItemProps> = ({label,children,required}) => {
  return (
    <View className="qz-ListItem">
      <View className="qz-ListItem__inner">
        <View className="qz-ListItem__inner-labelWrap">
          {required && <View style={{color: 'red'}}>*</View>}
          <View className="qz-ListItem__inner-label">{label}</View>
        </View>
        <View className="qz-ListItem__inner-value">{children}</View>
      </View>
    </View>
  )
}
export default ListItem