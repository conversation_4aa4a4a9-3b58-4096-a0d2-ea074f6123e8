@import '@styles/variables/default.scss';
@import '@styles/mixins/index.scss';

  .qz-ListItem{
    &-placeholder{
      color: #ABABAB;
    }
    padding: 0 15px 0 0;
    min-height: 60px;
    @include display-flex();
    @include align-items(center);
    @include hairline-bottom-relative($color-border-shallow, solid, 1px, 0, 0);
    &__inner{
      .native-picker__inner {
        padding:0;
        margin:0;
      }

      width:100%;
      @include display-flex();
      // padding-right: 20px;
      justify-content: space-between;
      &-labelWrap{
        display: flex;
        gap: 4px;
      }
      &-label{
        font-size: 16px;
        color: #2b2b2b;
        position: relative;
      }
      &-value{
        flex: 1;
        color: #2b2b2b;
        text-align: right;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding-left: 20px;
        // padding-right: 20px;
      }
      
    }
    &:last-child::after {
      display: none;
    }
  }