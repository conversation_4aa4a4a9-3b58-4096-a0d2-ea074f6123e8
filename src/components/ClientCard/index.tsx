import ClientBaseInfo from "@components/ClientBaseInfo";
import QzIcon from "@components/Icon";
import Panel from "@components/Panel";
import { Image, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { FC } from "react";
import "./index.scss";

type ClientCardProps = {
  data: PositionTypes;
};

const ClientCard: FC<ClientCardProps> = ({ data }) => {
  const { SCREEN_WIDTH } = Taro.getApp().$app;
  const { client, id: positionId } = data;
  const { name, id, logo, addressCoord, address } = client;
  const [latitude, longitude] = addressCoord.split(',') || [];

  return (
    <Panel title="公司信息">
      <View className="qz-clientCard">
        <View
          className="qz-clientCard__content"
          onClick={() => {
            Taro.navigateTo({
              url: `/packageA/pages/client/detail?id=${id}&positionId=${positionId}`
            });
          }}
        >
          {!!logo?.url && (
            <View className="qz-clientCard__logoWrap">
              <Image
                className="qz-clientCard__logo"
                src={logo.url}
                mode="scaleToFill"
              />
            </View>
          )}
          <View className="qz-clientCard__main">
            <View className="qz-clientCard__name">{name}</View>
            <ClientBaseInfo data={client} />
          </View>
          <View>
            <QzIcon className="card-cell__icon" type="icon_arrow" size="18" />
          </View>
        </View>

        {!!latitude && !!longitude && (
          <View className="qz-clientCard__map" onClick={()=>{
            wx.getLocation({
            type: "wgs84",
              success: function() {
                wx.openLocation({
                  latitude,
                  longitude,
                  name: address,
                  address: address
                });
              }
            });
          }}>
            <View className="qz-clientCard__map-address">
              <View>{address}</View>
            </View>
            
            <Image
              mode="aspectFill"
              style={{
                width: "100%",
                borderRadius: '10px',
                overflow:'hidden'
              }}
              src={`https://apis.map.qq.com/ws/staticmap/v2/?size=${SCREEN_WIDTH}*400&labels=size:12|color:black|anchor:3|offset:0_-32|bgcolor:red|xxx|${latitude},${longitude}&markers=size:large|color:blue|label:A|${latitude},${longitude}&key=MYZBZ-X26WG-4MYQG-IGSQN-HKUCS-O2FMU&center=${latitude},${longitude}&zoom=16&scale=2`}
            />
          </View>
        )}
      </View>
    </Panel>
  );
};
export default ClientCard;
