
@import '@styles/variables/default.scss';
@import '@styles/mixins/index.scss';

.qz-clientCard{
  &__content{
    display: flex;
    justify-content: center;
    align-items: center;
  }
  &__logoWrap{
    line-height: 0;
    margin-right: 16px;
    border: solid 1px #eee;
    border-radius: 2px;
  }
  &__logo{
    width:40px;
    height: 40px;
  }
  &__main{
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: space-between;
    gap: 4px;
  }
  &__name{
    font-size: 15px;
    font-weight: 500;
    color: $color-text;
  }
  &__map{
    position: relative;
    margin-top: 8px;
    &-address{
      width: 100%;
      position: absolute;
      top: 70px;
      display: flex;
      justify-content: center;
      View{
        padding: 6px 10px;
        display: inline-block;
        background-color: #fff;
        border-radius: 8px;
      }
    }
  }
}
