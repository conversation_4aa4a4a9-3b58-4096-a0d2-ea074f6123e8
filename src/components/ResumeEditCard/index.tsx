import IconFont from '@components/iconfont'
import { View } from '@tarojs/components'
import type { FC } from 'react'
import './index.scss'

type ResumeEditCardProps = {
  type?: 'add' | 'edit'
  title: string
  onEdit?: ()=>void
}
const ResumeEditCard: FC<ResumeEditCardProps> = ({type='add',title,children,onEdit}) => {
  return (
    <View className="qz-ResumeEditCard">
      <View className="qz-ResumeEditCard__hd" onClick={()=>{onEdit && onEdit()}}>
        <View className="qz-ResumeEditCard__hd-title">{title}</View>
        <View className="qz-ResumeEditCard__hd-add">
        <IconFont name={type === 'edit' ? "mp-arrow-right" : "add-circle" } color="#888" size={20} />
        </View>
      </View>
      <View className="qz-ResumeEditCard__bd">
        {children}
      </View>
      
    </View>
  )
}
export default ResumeEditCard