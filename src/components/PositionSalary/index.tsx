import { View } from '@tarojs/components';
import type { FC } from 'react';
import './index.scss';
import classNames from 'classnames';
import { useSelector } from 'react-redux';

type PositionSalaryProps = {
  positionData: PositionTypes
  className?: string;
};
const PositionSalary: FC<PositionSalaryProps> = ({ positionData,className }) => {
  const { salaryType, salaryBegin, salaryEnd } = positionData;
  const {dictMap={}} = useSelector((state:any) => state.dict);
  const salaryTypeMap = dictMap?.position?.salaryType[salaryType];
  let value;
  let unit;
  // salaryType为6表示面议
  if (salaryType === 6 || !salaryTypeMap || (!salaryBegin && !salaryEnd)) {
    value = '面议';
  } else {
    value =
      salaryBegin.toString() +
      (salaryBegin && salaryEnd ? '-' : '') +
      salaryEnd.toString();
    unit = '元/' + salaryTypeMap.substring(0, 1);
  }
  return (
    <View className={classNames(className, "qz-PositionSalary")}>
      <View className='qz-PositionSalary__value'>{value}</View>
      {unit ? <View className="qz-PositionSalary__unit">{unit}</View> : null}
    </View>
  );
};
export default PositionSalary;
