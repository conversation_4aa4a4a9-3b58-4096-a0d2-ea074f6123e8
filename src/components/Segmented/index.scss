.qz-Segmented{
  display: inline-block;
  background-color: #f5f5f5;
  border-radius: 6px;
  &-group{
    position: relative;
    display: flex;
    justify-items: flex-start;
    width: 100%;
    padding: 2px;
  }
  &__item{
    font-size: 12px;
    padding: 2px 10px;
    display: flex;
    gap: 4px;
    position: relative;
    transition: color .2s cubic-bezier(.645,.045,.355,1);
    border-radius: 4px;
    align-items: center;
    justify-content: center;
    &::after{
      content: "";
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      inset-inline-start: 0;
      border-radius: 4px;
      transition: background-color .2s;
    }
    &-selected{
      background-color: #fff;
      box-shadow: 0 1px 2px 0 rgb(0 0 0 / 3%), 0 1px 6px -1px rgb(0 0 0 / 2%), 0 2px 4px 0 rgb(0 0 0 / 2%);
      color: rgba(0,0,0,.88);
    }
  }
}