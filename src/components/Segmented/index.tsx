import { View } from '@tarojs/components'
import classNames from 'classnames'
import type { FC } from 'react'
import './index.scss'

type SegmentedProps = {
  value: string,
  options: any[]
  onChange?: (value:string)=>void
}
const Segmented: FC<SegmentedProps> = ({value,options,onChange}) => {
  return (
    <View className="qz-Segmented">
      <View className="qz-Segmented-group">
        {options.map((item)=>{
          return <View onClick={()=>{
            onChange && onChange(item.value)
          }} key={item.value} className={classNames("qz-Segmented__item",{
            'qz-Segmented__item-selected': value === item.value
          })}>
            <View className="qz-Segmented__item-icon"></View>
            <View className="qz-Segmented__item-label">{item.label}</View>
          </View>
        })}
        
      </View>
    </View>
  )
}
export default Segmented