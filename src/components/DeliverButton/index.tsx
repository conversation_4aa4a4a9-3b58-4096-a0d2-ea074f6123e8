import QzButton from '@components/Button';
import Taro from '@tarojs/taro';
import { FC } from 'react';
import { container, ui } from '@landrover/business/index';
import { getPhoneNumber } from '@utils/login';

type DeliverButtonProps = {
  id: number;
  className: string;
  size?: 'normal' | 'large' | 'small';
  onSuccess?: () => void;
  type?: string;
};
const DeliverButton: FC<DeliverButtonProps> = ({ id, className, onSuccess, type = 'secondary', size = 'normal' }) => {
  const userInfo = container.userService.auth || {};

  const deliver = () => {
    container.saasService
    .updateFlow({
      positionId: id,
    })
    .then(() => {
      const pOcSubscribe = userInfo.pOcSubscribe;
      if (!pOcSubscribe) {
        Taro.hideLoading();
        Taro.navigateTo({
          url: `/packageA/pages/result`,
        });
      } else {
        Taro.showToast({
          title: '报名成功',
          icon: 'success',
          duration: 2000,
        });
      }
      onSuccess && onSuccess();
    })
    .catch((res: any) => {
      return ui.showToast(res.errorMessage);
    });
  };

  const onSubmit = () => {
    Taro.showLoading();
    container.saasService.getResumeDetail(0).then((res: any) => {
      // 简历不存在或者简历不完善
      const { name, gender } = res || {};
      if (!name || !gender) {
        Taro.hideLoading();
        Taro.navigateTo({
          url: `/packageA/pages/resume/complete?positionId=${id}`,
        });
      } else {
        deliver();
      }
    })
    .catch(() => {
      Taro.hideLoading();
    });
  };

  let buttonProps: any = {};
  if (userInfo.phone) {
    buttonProps.onClick = onSubmit;
  } else {
    buttonProps = {
      openType: 'getPhoneNumber',
      onGetPhoneNumber: (e: any) => getPhoneNumber(e, () => onSubmit()),
    };
  }

  return (
    <QzButton
      className={className}
      circle
      fluid
      loading
      size={size}
      type={type}
      {...buttonProps}
    >
      立即报名
    </QzButton>
  );
};
export default DeliverButton;
