import QzModal from "@components/Modal";
import { Icon, Image, View } from "@tarojs/components";
import { FC, useState } from "react";
import { container } from "@landrover/business/index";
import { useEffect } from "react";
import Taro from "@tarojs/taro";
import "./index.scss";

type MpSubscribeProps = {
  visible: boolean;
  onCancel: () => void;
};
const MpSubscribe: FC<MpSubscribeProps> = ({ visible, onCancel }) => {
  // const { initialState } = useModel('@@initialState');
  const {
    extConfig,
  } = Taro.getApp().$app;
  const userInfo = container.userService.auth;
  const sign = userInfo?.sign;
  const [qrcodeUrl, setQrcodeUrl] = useState<string>('');
  useEffect(() => {
    if(visible){
      wx.downloadFile({ 
        header: {
          cookie: userInfo.cookie,
          'x-scene': 'MINIPROGRAM',
          'x-sign': extConfig.sign,
        },
        url: `https://candidate.lingzhao.net/api/wechat/qrcode?actionName=QR_STR_SCENE&sceneStr=bind:${sign}&timestamp=${(new Date()).getTime()}`,
        success: function(res) {
          setQrcodeUrl(res.tempFilePath);
        }
      });
      return ()=>{
        container.userService.updateUserProfile().then((res)=>{
        })
      }
    }
   
  }, [visible]);

  return (
    <QzModal
      // title="关注公众号"
      customContainer
      container-class="generate-image__modal"
      isSimple={false}
      isOpened={visible}
      cancelText="关闭"
      // confirmText="确定"
      // onConfirm={onOk}
      onCancel={onCancel}
      closeOnClickOverlay={false}
    >
      <View className="qz-mpSubscribe">
        <View className="qz-mpSubscribe__icon">
          <Icon size="64" type="success" />
        </View>
        <View className="qz-mpSubscribe__title">报名成功</View>
        <View className="qz-mpSubscribe__subtitle">
          长按关注公众号，我们将实时通知您报名结果
        </View>
        <View className="qz-mpSubscribe__image">
          <Image
            style={{
              border: "solid 1px #eee",
              width: "200px",
              height: "200px"
            }}
            showMenuByLongpress={true}
            src={qrcodeUrl}
          />
        </View>
        <View></View>
      </View>
    </QzModal>
  );
};
export default MpSubscribe;
