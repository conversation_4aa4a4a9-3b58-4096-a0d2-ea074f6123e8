import { View } from '@tarojs/components'
import QzIcon from '@components/Icon'
import './empty.scss'

type Props = {
  message: string
}
/**
 * 薪资展示.
 * @param {array} tags
 */
const Empty: FixFunctionComponent<Props, 'defaultProps'> = (props: Props) => {
  const { message } = props

  return (
    <View className={`empty`}>
      <QzIcon className="empty-icon" type="empty" size="100" />
      <View className="empty-text">{message}</View>
    </View>
  )
}
Empty.defaultProps = {
  message: '暂时还没有数据哦'
}

export default Empty
