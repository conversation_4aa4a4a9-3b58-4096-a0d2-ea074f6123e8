import { View } from '@tarojs/components'
import type { FC } from 'react'
import './index.scss'

type DescProps = {
  direction?: 'column' | 'row'
  style?: React.CSSProperties
}
const Desc: FC<DescProps> = ({children,style,direction='row'}) => {
  const defaultStyle:React.CSSProperties = {};
  defaultStyle.flexDirection = direction;
  if(direction === 'row'){
    defaultStyle.gap = '8px'
  }
  return (
    <View className="qz-Desc" style={{...defaultStyle,...style}}>
      {children}
    </View>
  )
}
export default Desc