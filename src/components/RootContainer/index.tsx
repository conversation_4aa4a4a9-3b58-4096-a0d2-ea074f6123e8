import { View } from '@tarojs/components'
import { container } from "@landrover/business/index"
import { useDispatch } from 'react-redux'
import { setLogin } from '@stores/actions/login';
import {setDictData} from '@stores/actions/dict';
import { FC, useEffect } from 'react'
import Taro from '@tarojs/taro';

type RootContainerProps = {}
const RootContainer: FC<RootContainerProps> = ({
  children
}) => {
  
  const dispatch = useDispatch()
  const wxLogin = ()=> {
    // 静默登录
    const isLogin = container.userService.isLogin();
    if (!isLogin) {
      wx.login({
        success: res => {
          const { code } = res;
          if (code) {
            const invitor = Taro.getStorageSync("invitor");
            container.userService
              .getWxAuthLogin({
                code,
                invitor
              })
              .then(() => {
                dispatch(setLogin({
                  isLogin: true
                }))
              })
              .catch(() => {});
          }
        },
        fail: res => {
        }
      });
    }else{
      dispatch(setLogin({
        isLogin: true
      }))
    }
  }
  const getDict = ()=>{
    const promise1 = container.saasService.getDictData()
    const promise2 = container.saasService.getDictData({
      type: "array"
    })

    return Promise.all([promise1, promise2]).then((res)=>{
      dispatch(setDictData({
        dictMap: res[0],
        dictArr: res[1]
      }))
    })

  }
  
  useEffect(() => {
    wxLogin();
    getDict();
  }, [])
  
  return (
    <View>
    {children}
    </View>
  )
}
export default RootContainer