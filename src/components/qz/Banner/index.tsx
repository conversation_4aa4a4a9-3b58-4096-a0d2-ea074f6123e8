import { Image, Swiper, SwiperItem, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { formatOssUrl } from '@utils/format';
import { getMaterialUrl } from '@utils/index';
import type { FC } from 'react';
import './index.scss'


type BannerProps = {};
const Banner: FC<BannerProps> = () => {
  const {
    TPL_CONFIG,
  } = Taro.getApp().$app
  const {list} = TPL_CONFIG.banner;

  return (
    <View className='qz-banner'>
        <Swiper
            className="qz-banner__swiper"
            circular
            indicatorDots={list?.length>1}
            indicatorColor='rgba(0, 0, 0, .3)'
            indicatorActiveColor='#000'
            interval={4000}
            duration={300}
            autoplay
        >
          {list.map((item:any)=>{
            const url = getMaterialUrl(item.link);
            return <SwiperItem key={item.image} className="qz-banner__swiper-item">
            <View onClick={()=>{
              Taro.navigateTo({
                url
              });
            }}>
            <Image src={formatOssUrl(item.image)} mode="scaleToFill" className="qz-banner__swiper-image" />
            </View>
          </SwiperItem>
          })}
        </Swiper>
    </View>
  );
};
export default Banner;
