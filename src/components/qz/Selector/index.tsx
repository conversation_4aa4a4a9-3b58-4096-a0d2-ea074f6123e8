import IconFont from '@components/iconfont';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { hexToRgbA } from "@utils/index";
import classNames from 'classnames';
import type { FC } from 'react'
import './index.scss'

type SelectorProps = {
  multi?: boolean
  values: any
  options: any[]
  name: string
  onChangeValues:(values:any)=>void
}
const Selector: FC<SelectorProps> = ({multi,values,options,name,onChangeValues}) => {
  const { TPL_CONFIG } = Taro.getApp().$app;
  const { primaryColor } = TPL_CONFIG.basic;

  const getNewValue = (itemValue: string, key: string) => {
    const value = values[key];
    let newValue: string | undefined;
    if (!value) {
      if (itemValue !== "0") {
        newValue = itemValue;
      } else {
        newValue = undefined;
      }
    } else {

      if (itemValue === value) {
        newValue = undefined;
      } else {
        if(itemValue === '0'){
          newValue = undefined;
        }else{
          newValue = itemValue;
        }
        
      }
    }
    return newValue;
  };

  const getNewValues = (itemValue: string, key: string) => {
    const valueArr = values[key] ? values[key].split(','):undefined;
    let newValue: string | undefined;
    if (!valueArr?.length) {
      if (itemValue !== "0") {
        newValue = itemValue;
      } else {
        newValue = undefined;
      }
    } else {
      if(valueArr.includes(itemValue)){
        const a = valueArr.filter((item)=>{return item !== itemValue})
        if(a.length){
          newValue = a.join(',')
        }else{
          newValue = undefined
        }
      }else{
        if(itemValue === '0'){
          newValue = undefined;
        }else{
          newValue = values[key] + `,${itemValue}`
        }
      }

    }
    return newValue;
  };

  return (
    <View className="qz-Selector">
            {options.map(item => {
              let isActive;
              if(multi){
                isActive = (values[name] && values[name].split(',').includes(item.value)) || (!values[name] && item.value === "0")
              }else{
                isActive = values[name] === item.value || (!values[name] && item.value === "0");
              }
              let activeStyle = {};
              if(isActive) activeStyle = {
                color: primaryColor,
                backgroundColor: hexToRgbA(primaryColor, 0.15)
              }
              return (
                <View
                  className={classNames(
                    "qz-Selector__item",
                    {
                      active: isActive
                    }
                  )}
                  style={activeStyle}
                  onClick={() => {
                    onChangeValues({
                      ...values,
                      [name]: multi ? getNewValues(item.value, name):getNewValue(item.value, name)
                    });
                  }}
                >
                  {item.label}
                  <View className="qz-Selector__item-icon">
                    <IconFont
                      name="jiaobiaoxuanzhong"
                      size={28}
                      color={primaryColor}
                    />
                  </View>
                </View>
              );
            })}
          </View>
  )
}
export default Selector