@import '@styles/variables/default.scss';

.qz-Selector{
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  &__item{
    position: relative;
    color: $color-text;
    width: 107px;
    height: 36px;
    font-size: 13px;
    text-align: center;
    line-height: 36px;
    background-color: #f6f6f6;
    &-icon{
      display: none;
      position: absolute;
      right:-3px;
      bottom:-3px;
    }
    
    &.active{
      .qz-Selector__item-icon{
        display: inline-block;
      }
    }
  }
}