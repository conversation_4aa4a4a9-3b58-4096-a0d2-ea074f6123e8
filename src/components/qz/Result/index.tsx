import { Icon, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import type { FC } from 'react'
import Text from '../Text';
import Title from '../Title';
import './index.scss'

type ResultProps = {
  status?: string;
  title: string;
  subTitle?: string;
  extra?: React.ReactNode;
}
const Result: FC<ResultProps> = ({status,title,subTitle,extra}) => {
  const { MENU_BUTTON, SCREEN_WIDTH } = Taro.getApp().$app;
  const { right, bottom } = MENU_BUTTON;
  const NAV_GAP = SCREEN_WIDTH - right;
  const navbarHeight = bottom + NAV_GAP;
  return (
    <View className="qz-Result" style={{
      height: "100vh",
      marginTop: -(navbarHeight + 24)
    }}>
      <View className="qz-Result__content">
      {!!status && <View className="qz-Result__content-icon">
          <Icon size="64" type={status} />
        </View>}
        {!!title && <Title level={3}>{title}</Title>}
        {!!subTitle && <Text type="secondary">{subTitle}</Text>}
        {!!extra && <View>
          {extra}
        </View>}
      </View>
        
      </View>
  )
}
export default Result