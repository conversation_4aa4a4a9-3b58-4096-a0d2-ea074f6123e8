import { Image, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import type { FC } from 'react';
import './index.scss';

type IndexLogoProps = {};
const IndexLogo: FC<IndexLogoProps> = () => {
  const { LOGO_SIZE, TPL_CONFIG, extConfig: { headImage } } = Taro.getApp().$app;

  if (!TPL_CONFIG.basic.logo.isShow) return null;
  return (
    <View className="qz-indexLogo" style={{ width: LOGO_SIZE, height: LOGO_SIZE, borderRadius: LOGO_SIZE / 2 }}>
      <Image src={headImage} style={{ width: LOGO_SIZE, height: LOGO_SIZE, display: 'block' }} mode="aspectFit" />
    </View>
  );
};
export default IndexLogo;
