import { View } from '@tarojs/components'
import type { FC } from 'react'
import './index.scss'

type RoundIconProps = {
  style?: React.CSSProperties
  size?: number
  backgroundColor?: string
  children: React.ReactNode
  onClick?: (e:any)=>void
}
const RoundIcon: FC<RoundIconProps> = ({style, size=24,backgroundColor='#ccc',children,onClick}) => {
  const customStyle:React.CSSProperties = {
    width: size+'px',
    height: size+'px',
    borderRadius: size+'px',
    backgroundColor,
    ...style
  };

  return (
    <View className="qz-RoundIcon" style={customStyle} onClick={onClick}>
      {children}
    </View>
  )
}
export default RoundIcon