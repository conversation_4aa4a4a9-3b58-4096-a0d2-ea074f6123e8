import type { FC } from 'react'
import { useEffect,useState } from 'react'
import { container, ui } from "@landrover/business/index";
import Taro, { useDidShow } from "@tarojs/taro";
import { View, ScrollView, Block } from "@tarojs/components";
import Category from '@components/qz/Category';
import Banner from '@components/qz/Banner';
import Notice from '@components/qz/Notice';
import PositionList from '../PositionList';
import PositionFilter from '@components/qz/PositionFilter';
import './index.scss';


type ScrollViewIndexProps = {
  onChangeScrollTop:(value:number)=>void;
  isRefresh?:boolean
  isBackTop?:boolean
  filterValues: any
  onChangeFilterVisible?:(value:boolean)=>void
  onChangeFilterValues?: (values: any) => void;
};
const ScrollViewIndex: FC<ScrollViewIndexProps> = ({onChangeScrollTop,isRefresh=false,isBackTop,filterValues,onChangeFilterVisible,onChangeFilterValues}) => {
  const {
    TPL_CONFIG,
    SCREEN_WIDTH,
    MENU_BUTTON,
  } = Taro.getApp().$app;
  const {primaryColor} = TPL_CONFIG.basic;
  const {right,bottom} = MENU_BUTTON;
  const NAV_GAP = SCREEN_WIDTH - right;
  const navbarHeight = bottom + NAV_GAP;

  const [scrollComplete, setScrollComplete] = useState<boolean>(false)
  const [pageIndex,setPageIndex] = useState<number>(1); 
  const [totalNumber,setTotalNumber] = useState<number>(0);
  const [listData,setListData] = useState<PositionTypes[]>([]);
  const [positionFilterTop, setPositionFilterTop] = useState<number>(0);
  const [positionFilterFixed,setPositionFilterFixed] = useState<boolean>(false);

  useEffect(() => {
    if(listData?.length && !positionFilterTop){
      Taro.nextTick(() => {
        const query = Taro.createSelectorQuery();
        query.select(`#position-filter`).boundingClientRect();
        query.exec(function(res) {
          setPositionFilterTop(res[0].top);
        });
      })
    }
  }, [listData]);

  useEffect(()=>{
    if(isRefresh){
      setScrollComplete(false);
      getPositionList({
        current: 1
      });
    }
  },[isRefresh,filterValues])

  useDidShow(()=>{
    if(isRefresh){
      setScrollComplete(false);
      getPositionList({
        current: 1
      });
    }
  })

  const getPositionList = (params,callback?:any)=>{
    container.saasService
    .getPositions({
      pageSize: 10,
      ...filterValues,
      ...params
    })
    .then((result: { list: any[]; current: number; total: number }) => {
     const {current,list,total}  = result;
      
     setPageIndex(current);
     setListData(current===1?list:listData.concat(list));
     setTotalNumber(total);
     callback && callback();
    }).catch((res)=>{
    });
  }

  const onScrollToLower = () => {
    const getLastPageIndex = Math.ceil(totalNumber / 10);

    // 判断是否有下一页数据
    const hasNext = pageIndex < getLastPageIndex;
    // 没有下一页，无数加载更多数据
    if (!hasNext) {
      setScrollComplete(true);
      return;
    }

    const getPageIndex = pageIndex + 1;
    getPositionList({
      current: getPageIndex
    });
  }

  const onScroll = (e)=>{
    const currentScrollTop = e.detail.scrollTop;
    onChangeScrollTop(currentScrollTop);
    setPositionFilterFixed(currentScrollTop > (positionFilterTop-(navbarHeight+40)));
  }

 
  const [refresherTriggered,setRefresherTriggered] = useState<boolean>(false)
  const onRefresherRefresh = ()=>{
    setRefresherTriggered(true)
    getPositionList({
      current: 1
    },()=>{
      refreshCallback();
    });
  }

  const refreshCallback = ()=>{
    setTimeout(()=>{
      ui.showToast('职位已更新','none',1000)
      setRefresherTriggered(false)
    },500)
  }

  const handleUpdateListData = (id: number)=> {
    const data = [...listData];
    data.forEach(item => {
      if (item.id === id) {
        item.flow = {
          id
        };
      }
    });
    setListData(data);
  }
  let scrollViewProps = {};
  if(isBackTop) scrollViewProps.scrollTop = 0;

  const PositionFilterComp = (
    <PositionFilter
      filterValues={filterValues}
      onChangeFilterValues={values => {
        onChangeFilterValues && onChangeFilterValues(values);
      }}
      onChangeFilterVisible={value => {
        onChangeFilterVisible && onChangeFilterVisible(value);
      }}
    />
  );

  return (
    <Block>
      <View>
        {positionFilterFixed ? <View style={{
          position:'fixed',
          top: 0,
          left: 0,
          width: '100%',
          zIndex: 9
        }}>
          {PositionFilterComp}
        </View>:null}
      </View>
      <ScrollView
        className="qz-scrollViewIndex"
        style={{
          position:'relative',
          height: '100%'
        }}
        refresherEnabled
        lowerThreshold={200}
        onRefresherRefresh={onRefresherRefresh}
        refresherBackground={primaryColor}
        refresherTriggered={refresherTriggered}
        scrollY
        scrollWithAnimation
        onScrollToLower={onScrollToLower}
        onScroll={onScroll}
        {...scrollViewProps}
      >
        {/* 背景 */}
        <View className="qz-scrollViewIndex__bg" style={{
          backgroundColor: primaryColor
        }}></View>
        <View className="qz-scrollViewIndex__content">
        <Banner />
        <Category />
        <Notice />
        <View className="qz-scrollViewIndex__positionListWrap">
          <View id="position-filter">
            {PositionFilterComp}
          </View>
          <PositionList data={listData} onChangeListData={handleUpdateListData} scrollComplete={scrollComplete} />
        </View>
        </View>
      </ScrollView>
      </Block>
         
  );
};
export default ScrollViewIndex;
