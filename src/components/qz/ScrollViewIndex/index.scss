@import '@styles/variables/default.scss';


.qz-scrollViewIndex{
  &__bg{
    position: absolute;
    top:0;
    left:50%;
    width: 110vw;
    margin-left: -55vw;
    height: 100px;
    border-bottom-left-radius: 40px;
    border-bottom-right-radius: 40px;
  }
  &__item{
    border-radius: 10px;

  }
  &__content{
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 0 15px 15px 15px;
  }
  &__positionListWrap{
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
  }
  &__list{
    border-radius: 10px;
    overflow: hidden;
    background-color: #fff;
  }
}
