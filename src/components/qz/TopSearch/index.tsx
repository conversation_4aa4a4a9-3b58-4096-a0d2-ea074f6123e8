import { View } from '@tarojs/components'
import Taro from '@tarojs/taro'
import classNames from 'classnames'
import { FC } from 'react'
import SwiperSearch from '../SwiperSearch'
import './index.scss'

type TopSearchProps = {
  style?: React.CSSProperties
  mode?: string
}
const TopSearch: FC<TopSearchProps> = ({ style, mode = 'dark' }) => {
  const {
    SCREEN_WIDTH,
    MENU_BUTTON,
    TPL_CONFIG,
  } = Taro.getApp().$app
  const {top,right,left,bottom} = MENU_BUTTON
  const {primaryColor} = TPL_CONFIG.basic;
  const NAV_GAP = SCREEN_WIDTH - right
  const navbarHeight = bottom + NAV_GAP
  const searchStyle = {
    paddingTop: top,
    paddingLeft: NAV_GAP,
    height: navbarHeight,
  }
  if(mode === 'dark') searchStyle.backgroundColor = primaryColor
 
  return (
    <View
      className={classNames('qz-topSearch', {
        'qz-topSearch__style-dark': mode === 'dark',
        'qz-topSearch__style-light': mode === 'light',
      })}
      style={{...searchStyle,...style}}>
        
      <SwiperSearch style={{
        width: left - 2 * NAV_GAP + 'px'
      }} />
    </View>
  )
}
export default TopSearch
