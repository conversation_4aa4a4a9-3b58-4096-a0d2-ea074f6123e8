import LoadMore from '@components/LoadMore'
import NoMore from '@components/NoMore'
import { ScrollView } from '@tarojs/components'
import { FC, Fragment } from 'react'

type ScrollViewWrapProps = {
  children: React.ReactNode
  style?: React.CSSProperties
  refresherTriggered?: boolean
  refresherBackground?: string
  scrollComplete?: boolean
  onScroll: (e:any)=>void
  onChangeScrollToLower:()=>void
  onChangeRefresherRefresh: ()=>void
  isBackTop?:boolean
}
const ScrollViewWrap: FC<ScrollViewWrapProps> = ({
  children,
  style,
  refresherTriggered,
  refresherBackground,
  onChangeRefresherRefresh,
  onChangeScrollToLower,
  onScroll,
  scrollComplete,
  isBackTop
}) => {
  let scrollViewProps = {};
  if(isBackTop) scrollViewProps.scrollTop = 0;
  return (
   
      <ScrollView
          className="qz-scrollViewIndex"
          style={{
            position:'relative',
            height: '100%',
            ...style
          }}
          refresherEnabled
          onRefresherRefresh={onChangeRefresherRefresh}
          refresherBackground={refresherBackground}
          refresherTriggered={refresherTriggered}
          lowerThreshold={200}
          scrollY
          scrollWithAnimation
          onScrollToLower={onChangeScrollToLower}
          onScroll={onScroll}
          {...scrollViewProps}
        >
          <Fragment>
            {children}
            {scrollComplete ? <NoMore style={{
              backgroundColor:'#f5f5f5'
            }}>没有更多数据了</NoMore>:<LoadMore />}
          </Fragment>
    </ScrollView>

  )
}
export default ScrollViewWrap