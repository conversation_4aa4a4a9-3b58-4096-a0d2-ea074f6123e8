import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import classNames from 'classnames';
import React, { FC } from 'react';
import './index.scss';

type IndexTitleProps = {
  style?: React.CSSProperties
};
const FAMILY_MAP = {
  'classic': '',
  'default': '',
  'mt': '',
  'verdant': '',
  'xmla': '',
};
const IndexTitle: FC<IndexTitleProps> = ({ style = {} }) => {
  const { TPL_CONFIG, MENU_BUTTON: { width }, LOGO_SIZE, NAV_BAR_WIDTH } = Taro.getApp().$app;
  const { title: { text, position }, logo: { isShow } } = TPL_CONFIG.basic;
  const titleWidth = NAV_BAR_WIDTH - width - (isShow ? (LOGO_SIZE + 8) : 0);
  const themeType = TPL_CONFIG.theme.value;

  if (FAMILY_MAP[themeType]) {
    Taro.loadFontFace({ family: 'jyhphy', source: 'url("https://candidate.lingzhao.net/ChuangKeTieJinGangTi.otf")' }).then();
    style.fontFamily = 'jyhphy';
  }

  return (
    <View className={classNames('qz-indexTitle')} style={{
      color: 'white',
      whiteSpace: 'nowrap',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      justifyContent: 'start',
      display: 'inline-block',
      paddingLeft: position === 'left' && !isShow ? 10 : 0,
      maxWidth: position === 'left' ? titleWidth : titleWidth - width,
      ...style,
    }}
    >
      {text}
    </View>
  );
};
export default IndexTitle;
