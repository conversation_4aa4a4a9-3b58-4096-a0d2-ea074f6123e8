import { View } from '@tarojs/components'
import type { FC } from 'react'
import React from 'react'
import './index.scss'

type TitleProps = {
  level: 1|2|3|4|5,
  children: React.ReactNode
  style?:React.CSSProperties
}
const Title: FC<TitleProps> = ({level,children,style}) => {
  return (
    <View style={style} className={`qz-Title qz-Title__h${level}`}>
      {children}
    </View>
  )
}
export default Title