import {  Image, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { FC } from 'react'
import Text from '../Text';
import Title from '../Title';
import './index.scss'

const WelcomePage: FC = () => {
  const {
    extConfig,
  } = Taro.getApp().$app;

  
  const {headImage,nickname} = extConfig;
  return <View className="qz-WelcomePage">
      <View className="qz-WelcomePage__content">
        <View className="qz-WelcomePage__content__main">
          <Image
            className="qz-WelcomePage__logo"
            style={{
              border: "solid 1px #eee",
              width: "100px",
              height: "100px"
            }}
            showMenuByLongpress={true}
            src={headImage}
          />
          <Title level={3}>{nickname}</Title>
          <Text type="secondary" style={{
            letterSpacing: '4px',
            fontSize:'12px'
          }}>轻松找到好工作</Text>
        </View>
        <View>
          <View className="qz-WelcomePage__btn" onClick={()=>{
            Taro.switchTab({
              url: "/pages/index/index"
            });
          }}>开启求职之旅</View>
        </View>
        
      </View>
    </View>
    }
export default WelcomePage