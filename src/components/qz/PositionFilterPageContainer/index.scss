@import '@styles/variables/default.scss';

.qz-PositionFilterPageContainer{
  padding: 20px;
  color: $color-text;
  &__hd{
    position: relative;
    display: flex;
    width: 100%;
    padding-bottom: 10px;
    justify-content: center;
    &-close{
      position: absolute;
      right:0;
      margin: -20px;
      padding: 20px;
    }
  }
  &__bd{
    height: 72vh;
    overflow-y: auto;
    margin-right: -20px;
    padding-right: 20px;
    padding: 0px 0 10px;
    
  }
  &__btnGroup{
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
    width: 100%;
    gap: 10px;
  }
  &__checkboxGroup{
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    &-item{
      position: relative;
      width: 107px;
      height: 36px;
      font-size: 14px;
      text-align: center;
      line-height: 36px;
      background-color: #f6f6f6;
      &-icon{
        display: none;
        position: absolute;
        right:-3px;
        bottom:-3px;
      }
      
      &.active{
        .qz-PositionFilterPageContainer__checkboxGroup-item-icon{
          display: inline-block;
        }
      }
    }
  }
}
