import QzButton from '@components/Button';
import IconFont from '@components/iconfont';
import Popup from '@components/Popup';
import { View } from '@tarojs/components';
import Taro, { getCurrentPages } from '@tarojs/taro';
import React, { FC, useEffect, useState } from 'react';
import Selector from '../Selector';
import Title from '../Title';
import './index.scss';

type PositionFilterPageContainerProps = {
  config: any;
  visible: boolean;
  values: any;
  onCancel: () => void;
  onChange: (values: any) => void;
};
const titleStyle: React.CSSProperties = {
  fontWeight: 'normal',
  padding: '20px 0 10px',
};
const salaryOptions = [
  { value: '0', label: '不限' },
  { value: '0,1000', label: '1000元以下' },
  { value: '1000,2000', label: '1000-2000元' },
  { value: '2000,3000', label: '2000-3000元' },
  { value: '3000,5000', label: '3000-5000元' },
  { value: '5000,8000', label: '5000-8000元' },
  { value: '8000,12000', label: '8000-12000元' },
  { value: '12000,20000', label: '12000-20000元' },
  { value: '20000,25000', label: '20000-25000元' },
  { value: '25000', label: '25000元以上' },
];
const advantagesOptions = [
  { value: '0', label: '不限' },
  { value: '节日福利', label: '节日福利' },
  { value: '五险一金', label: '五险一金' },
  { value: '补充医疗保险', label: '补充医疗保险' },
  { value: '加班补助', label: '加班补助' },
  { value: '交通补助', label: '交通补助' },
  { value: '通讯补助', label: '通讯补助' },
  { value: '住宿补贴', label: '住宿补贴' },
  { value: '月结', label: '月结' },
  { value: '日结', label: '日结' },
  { value: '年底双薪', label: '年底双薪' },
  { value: '包工作餐', label: '包工作餐' },
  { value: '周末双休', label: '周末双休' },
  { value: '包住宿', label: '包住宿' },
  { value: '就餐补贴', label: '就餐补贴' },
];
const degreeOptions = [
  { value: '0', label: '不限', checked: true },
  { value: '1', label: '初中' },
  { value: '2', label: '职高/高中' },
  { value: '3', label: '中专/中技' },
  { value: '4', label: '专科' },
  { value: '5', label: '本科' },
  { value: '6', label: '硕士' },
  { value: '7', label: '博士' },
  { value: '8', label: '博士后' },
];
const workYearsOptions = [
  { value: '0', label: '不限', checked: true },
  {
    label: '1年以下',
    value: '1',
  },
  {
    label: '1-3年',
    value: '2',
  },
  {
    label: '3-5年',
    value: '3',
  },
  {
    label: '5-10年',
    value: '4',
  },
  {
    label: '10年以上',
    value: '5,',
  },
];
const PositionFilterPageContainer: FC<PositionFilterPageContainerProps> = ({ config, visible, values, onCancel, onChange }) => {
  const [filterValues, setFilterValues] = useState<any>(values);
  const isTabBarPage = wx.getTabBar?.(getCurrentPages().pop()) || false;

  useEffect(() => {
    if (visible) {
      isTabBarPage && Taro.hideTabBar();
      setFilterValues(values);
    } else {
      isTabBarPage && Taro.showTabBar();
    }
  }, [isTabBarPage, values, visible]);

  return (
    // <PageContainer
    //   show={visible}
    //   position={"bottom"}
    //   onBeforeEnter={() => {
    //     Taro.hideTabBar();
    //   }}
    //   onLeave={() => {
    //     Taro.showTabBar();
    //   }}
    //   onAfterLeave={() => {
    //     onCancel();
    //   }}
    //   round={true}
    // >
    <Popup isOpened={visible} isShowClose={false}>
      <View className="qz-PositionFilterPageContainer">
        <View className="qz-PositionFilterPageContainer__hd">
          <Title level={5}>职位筛选</Title>
          <View className="qz-PositionFilterPageContainer__hd-close" onClick={() => onCancel()}><IconFont name="close" size={24}></IconFont></View>
        </View>
        {config && <View>
          <View className="qz-PositionFilterPageContainer__bd">
            <Title level={5} style={{ ...titleStyle, paddingTop: 0 }}>
              期望月薪（单选）
            </Title>
            <Selector name="salary" options={salaryOptions} values={filterValues} onChangeValues={setFilterValues} />

            <Title level={5} style={titleStyle}>
              职位福利（多选）
            </Title>
            <Selector name="advantages" multi options={advantagesOptions} values={filterValues} onChangeValues={setFilterValues} />

            <Title level={5} style={titleStyle}>
              工作年限（单选）
            </Title>
            <Selector name="workYears" options={workYearsOptions} values={filterValues} onChangeValues={setFilterValues} />

            <Title level={5} style={titleStyle}>
              学历要求（单选）
            </Title>
            <Selector name="degree" options={degreeOptions} values={filterValues} onChangeValues={setFilterValues} />
          </View>
          <View className="qz-PositionFilterPageContainer__btnGroup">
            <QzButton
              type="secondary"
              onClick={() => {
                setFilterValues({});
              }}
              circle
            >
              重置
            </QzButton>
            <QzButton
              type="primary"
              onClick={() => {
                onChange(filterValues);
                onCancel();
              }}
              circle
              customStyle={{
                flex: 1,
              }}
            >
              确定
            </QzButton>
          </View>
        </View>}
      </View>
    </Popup>
    // </PageContainer>
  );
};
export default PositionFilterPageContainer;
