@import '@styles/variables/default.scss';

.qz-scrollNav{
  position: fixed;
  left:0;
  &__item{
    display: inline-flex;
    height: 100%;
    margin: 0 12px;
    justify-content: center;
    align-items: center;
    font-weight: 200;
    &.active{
      // font-size: 18px;
      font-weight: 500;
    }
  }
  &__style{
    &-dark{
      .qz-scrollNav__item{
        color: #fff;
      }
    }
    &-light{
      background-color: #fff;
      .qz-scrollNav__item{
        color: #000;
      }
    }
  }
  &__line{
    display: inline-block;
    position: absolute;
    
    // width: 18px;
    height: 4px;
    border-radius: 2px;
    transition: all 0.3s;
  }
}