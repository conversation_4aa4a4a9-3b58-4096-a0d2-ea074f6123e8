import { ScrollView, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import classNames from "classnames";
import { FC, useState } from "react";
import { useEffect } from "react";
import "./index.scss";

type ScrollNavProps = {
  mode?: string;
  currentIndex?: number;
  onChangePageIndex: (index: number) => void;
};
const ScrollNav: FC<ScrollNavProps> = ({
  currentIndex = 0,
  onChangePageIndex,
  mode = "dark"
}) => {
  const {
    SCREEN_WIDTH,
    MENU_BUTTON,
    TPL_CONFIG
  } = Taro.getApp().$app;
  const {right,bottom} = MENU_BUTTON;
  const {primaryColor} = TPL_CONFIG.basic;
  const {list} = TPL_CONFIG.nav || {};
  const NAV_GAP = SCREEN_WIDTH - right;
  const navbarHeight = bottom + NAV_GAP;


  const [scrollLeft, setScrollLeft] = useState<number>(0);
  const [scrollOffsetLeft, setOffsetScrollLeft] = useState<number>(0);
  const onScroll = e => {
    setScrollLeft(e.detail.scrollLeft);
  };

  const [leftValue, setLeftValue] = useState<number>(0);
  useEffect(() => {
    const query = Taro.createSelectorQuery();
    query.select(`#tab${currentIndex}`).boundingClientRect();
    query.exec(function(res) {
      if (res[0]?.left) {
        setLeftValue(res[0].left + (res[0].width - 18) / 2 + scrollLeft);
      } else {
        setLeftValue((64 - 18) / 2);
      }
    });

    if(currentIndex>2){
      const l = (currentIndex-2)*70;
      setOffsetScrollLeft(l)
    }else{
      setOffsetScrollLeft(0)
    }
  }, [currentIndex]);

  const navStyle = {
    top: MENU_BUTTON.bottom,
    height: "40px",
    boxSizing:'content-box'
  }
  if(mode === 'dark'){
    navStyle.backgroundColor = primaryColor
  }


  return (
    <View
      className={classNames("qz-scrollNav", {
        "qz-scrollNav__style-dark": mode === "dark",
        "qz-scrollNav__style-light": mode === "light"
      })}
      style={navStyle}
    >
      <ScrollView
        scrollX
        scrollWithAnimation
        scrollLeft={scrollOffsetLeft}
        onScroll={onScroll}
        
        style={{
          width: "100vw",
          whiteSpace: "nowrap",
          height: "100%",
          position: "relative"
        }}
      >
        {list.map((item, index) => {
          return (
            <View
              id={`tab${index}`}
              key={item.name}
              onClick={() => {
                onChangePageIndex(index);
              }}
              className={classNames("qz-scrollNav__item", {
                active: currentIndex === index
              })}
            >
              {item.name}
            </View>
          );
        })}
        <View
          className={classNames("qz-scrollNav__line", {
            'dark': mode === "dark",
            'light': mode === "light"
          })}
          style={{
            left: leftValue,
            width: 18,
            top: 30,
            backgroundColor: mode ===  'light' ? primaryColor : '#fff'
          }}
        ></View>
      </ScrollView>
    </View>
  );
};
export default ScrollNav;
