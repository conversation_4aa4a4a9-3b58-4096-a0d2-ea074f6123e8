import QzButton from '@components/Button'
import IconFont from '@components/iconfont'
import { Swiper, SwiperItem, Text, View } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { getMaterialUrl } from '@utils/index'
import type { FC } from 'react'
import './index.scss'

type NoticeProps = {
  style?:React.CSSProperties
}
const Notice: FC<NoticeProps> = ({style}) => {
  const { TPL_CONFIG } = Taro.getApp().$app
  const { isShow, list  } = TPL_CONFIG.notice
  const themeValue = TPL_CONFIG.theme.value;
  if (!isShow || !list?.length) return null;
  return (
    <View className="qz-notice" style={style}>
      <IconFont  name="mp-notice" color={'#333333'} size={16} />
      <Swiper
        className="qz-notice__swiper"
        vertical
        circular
        // indicatorDots
        interval={3500}
        duration={300}
        autoplay>
        {list.map((item) => {
          const {title,link,button} = item || {};
          const url = getMaterialUrl(item.link);
          return (
            <SwiperItem key={item.title}>
              <View className="qz-notice__swiper-item" onClick={()=>{
              if(link){
                Taro.navigateTo({
                  url
                });
              }
            }}>
              <Text>{title}</Text>
              {!!button?.text && <QzButton type={['mt','verdant'].includes(themeValue)?"primary":"secondary"} size="small" circle>
                {button.text}
                </QzButton>}
                </View>
            </SwiperItem>
          )
        })}
      </Swiper>
    </View>
  )
}
export default Notice
