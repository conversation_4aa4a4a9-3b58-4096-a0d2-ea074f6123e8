import IconFont from "@components/iconfont";
import Badge from "@components/qz/Badge";
import Segmented from "@components/Segmented";
import { setListConfig } from "@stores/actions/list";
import { Picker, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import classNames from "classnames";
import { FC, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import "./index.scss";

type PositionFilterProps = {
  filterValues: any;
  simple?: boolean;
  onChangeFilterVisible: (value: boolean) => void;
  onChangeFilterValues?: (values: any) => void;
};
const PositionFilter: FC<PositionFilterProps> = ({
  filterValues,
  simple = false,
  onChangeFilterVisible,
  onChangeFilterValues
}) => {
  const { dictMap = {}, dictArr = {} } = useSelector(
    (state: any) => state.dict
  );
  const salaryOptions = [
    { id: "0", name: "不限" },
    { id: "0,1000", name: "1000元以下" },
    { id: "1000,2000", name: "1000-2000元" },
    { id: "2000,3000", name: "2000-3000元" },
    { id: "3000,5000", name: "3000-5000元" },
    { id: "5000,8000", name: "5000-8000元" },
    { id: "8000,12000", name: "8000-12000元" },
    { id: "12000,20000", name: "12000-20000元" },
    { id: "20000,25000", name: "20000-25000元" },
    { id: "25000", name: "25000元以上" }
  ];
  const cityMap = dictMap?.common?.city;
  const cityArr = dictArr?.common?.city;
  const [cityPickerOptions, setCityPickerOptions] = useState<any>([]);
  const [value, setValue] = useState<any[]>([0, 0]);
  const [salaryValue, setSalaryValue] = useState<any[]>([0]);

  const dispatch = useDispatch();
  const { TPL_CONFIG } = Taro.getApp().$app;
  const modeValue = TPL_CONFIG?.list?.mode || "list";
  const recruitment = TPL_CONFIG?.recruitment;
  const { mode } = useSelector((state: any) => state.list);
  const count = Object.keys(filterValues).filter(item => {
    return filterValues[item] !== undefined && item !== "city";
  });

  useEffect(() => {
    if (!mode) {
      dispatch(
        setListConfig({
          mode: modeValue
        })
      );
    }
  }, []);

  const getFirstCol = () => {
    const provinceDictData = cityArr.filter(
      item => item.id === recruitment.province
    );
    const currentCity = [{ id: 0, name: "不限" }];
    provinceDictData[0].children.forEach(item => {
      if (recruitment.city.includes(item.id)) {
        currentCity.push({
          id: item.id,
          name: item.name,
          children: item.children
        });
      }
    });
    return currentCity;
  };

  const getCityPickerOptions = () => {
    return getFirstCol();
  };
  useEffect(() => {
    if (recruitment?.city) {
      const newCityPickerOptions = getCityPickerOptions();
      setCityPickerOptions([newCityPickerOptions, []]);
    }
  }, []);
  return (
    <View className="qz-PositionFilter">
      <View className="qz-PositionFilter__tabs">
        <View className="active">{simple ? '职位列表':'全部职位'}</View>

        {/* <View>附近</View>
        <View>最新</View> */}
      </View>
      <View className="qz-PositionFilter__content">
        {!simple && (
          <Segmented
            value={mode}
            onChange={value => {
              dispatch(
                setListConfig({
                  mode: value
                })
              );
            }}
            options={[
              {
                label: "列表",
                value: "list"
              },
              {
                label: "卡片",
                value: "card"
              }
            ]}
          />
        )}
        {recruitment?.city?.length && (
          <Picker
            rangeKey="name"
            mode="multiSelector"
            range={cityPickerOptions}
            value={value}
            onChange={e => {
              const [firstCol, secondCol] = e.detail.value;
              setValue([firstCol, secondCol]);
              onChangeFilterValues &&
                onChangeFilterValues({
                  ...filterValues,
                  city:
                    firstCol === 0
                      ? undefined
                      : cityPickerOptions[1][secondCol].id
                });
            }}
            onColumnChange={e => {
              const { column, value } = e.detail;
              if (column === 0) {
                if (value === 0) {
                  setCityPickerOptions([cityPickerOptions[0], []]);
                  setValue([0, 0]);
                } else {
                  setCityPickerOptions([
                    cityPickerOptions[0],
                    cityPickerOptions[0][value].children
                  ]);
                  setValue([value, 0]);
                }
              }
            }}
          >
            {simple ? (
              <View className="qz-PositionFilter__picker-button">
                <View className="qz-PositionFilter__picker-button-text">{filterValues.city ? cityMap[filterValues.city].name : "城市"}</View>
                <IconFont name="arrowdownb" size={14} color="666" />
              </View>
            ) : (
              <View className="qz-PositionFilter__picker">
                <IconFont name="city" size={14} color="666" />
                <View className="qz-PositionFilter__picker-name">
                  {filterValues.city ? cityMap[filterValues.city].name : "城市"}

                </View>
              </View>
            )}
          </Picker>
        )}
        {simple && (
          <Picker
            mode="selector"
            rangeKey="name"
            range={salaryOptions}
            value={salaryValue}
            onChange={e => {
              const { value } = e.detail;
              onChangeFilterValues &&
                onChangeFilterValues({
                  ...filterValues,
                  salary:
                    salaryOptions[value].id === "0"
                      ? undefined
                      : salaryOptions[value].id
                });
              setSalaryValue([value]);
            }}
          >
            <View className="qz-PositionFilter__picker-button">
              <View className="qz-PositionFilter__picker-button-text">
              {salaryValue[0] && salaryValue[0] !== "0"
                ? salaryOptions[salaryValue[0]]?.name
                : "工资"}
              </View>
                <IconFont name="arrowdownb" size={14} color="666" />
            </View>
          </Picker>
        )}

        {!simple && (
          <View className="qz-PositionFilter__more">
            <IconFont name="shaixuan2" size={14} color="262626" />
            <Badge count={count?.length}>
              <View
                className="qz-PositionFilter__more-text"
                onClick={() => {
                  onChangeFilterVisible && onChangeFilterVisible(true);
                }}
              >
                筛选
              </View>
            </Badge>
          </View>
        )}
      </View>
    </View>
  );
};
export default PositionFilter;
