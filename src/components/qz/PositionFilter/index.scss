@import '@styles/variables/default.scss';

.qz-PositionFilter{
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  align-items: center;
  &__picker{
    display: flex;
    align-items: center;
    gap: 2px;
    font-size: 14px;
    &-button{
      display: flex;
      align-items: center;
      gap: 4px;
      border: solid 1px #888;
      padding: 4px 8px;
      background-color: #f5f5f5;
      border-radius: 6px;
      color: #333;
      font-size: 14px;
      &-text{
        max-width: 80px;
        color: $color-text;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    &-name{
      max-width: 60px;
      color: $color-text;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    
  }
  &__tabs{
    display: flex;
    gap: 15px;
    font-size: 14px;
    color: $color-text;
    align-items: center;
    .active{
      font-weight: bold;
    }
  }
  &__content{
    display: flex;
    gap: 8px;
    align-items: center;
  }
  &__more{
    position: relative;
    display: flex;
    align-items: center;
    gap: 3px;
    &-text{
      font-size: 14px;
      color: $color-text;
    }
  }
}