import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import React, { FC } from 'react';
import './index.scss';

type IndexNavProps = {
  searchFixed: boolean
  children: React.ReactNode
  style?: React.CSSProperties
};
const IndexNav: FC<IndexNavProps> = ({ searchFixed, children, style }) => {
  const { STATUS_BAR_HEIGHT, NAV_BAR_HEIGHT, NAV_GAP, TPL_CONFIG } = Taro.getApp().$app;
  const { basic: { primaryColor, title: { position = 'start' } } } = TPL_CONFIG;

  return (
    <View
      className="qz-indexNav"
      style={{
        paddingLeft: NAV_GAP,
        paddingRight: NAV_GAP,
        paddingTop: STATUS_BAR_HEIGHT,
        height: NAV_BAR_HEIGHT + STATUS_BAR_HEIGHT,
        backgroundColor: searchFixed ? '#f5f5f5' : primaryColor,
        ...style,
      }}
    >
      <View className="qz-indexNav__content" style={{ height: NAV_BAR_HEIGHT + 'px', lineHeight: NAV_BAR_HEIGHT + 'px', justifyContent: position }}>
        {children}
      </View>
    </View>
  );
};
export default IndexNav;
