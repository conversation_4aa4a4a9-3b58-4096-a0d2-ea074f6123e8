import QzIcon from "@components/Icon";
import IconFont from "@components/iconfont";
import { Swiper, SwiperItem, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import classNames from "classnames";
import { FC, useEffect, useState } from "react";
import "./index.scss";

type SwiperSearchProps = {
  style?: React.CSSProperties;
};
const SwiperSearch: FC<SwiperSearchProps> = ({ style }) => {
  const { MENU_BUTTON, TPL_CONFIG } = Taro.getApp().$app;
  const { isSwiper, list } = TPL_CONFIG.search;
  const [searchValue, setSearchValue] = useState<any>();
  useEffect(() => {
    if (isSwiper && list?.length) {
      setSearchValue(list[0].name);
    }
  }, [list, isSwiper]);
  return (
    <View
      className={classNames("qz-swiperSearch", {})}
      onClick={() => {
        Taro.navigateTo({
          url: `/pages/index/search${
            searchValue ? `?keyword=${encodeURIComponent(searchValue)}` : ""
          }`
        });
      }}
      style={{
        height: MENU_BUTTON.height,
        ...style
      }}
    >
      <IconFont name="mp-search" color={'#999999'} size={16} />
      {isSwiper && list?.length ? (
        <Swiper
          className="qz-swiperSearch__swiper"
          vertical
          circular
          // indicatorDots
          interval={3000}
          duration={300}
          onChange={e => {
            setSearchValue(list[e.detail.current]);
          }}
          autoplay
        >
          {list.map(item => {
            return (
              <SwiperItem key={item} className="qz-swiperSearch__swiper-item">
                <View>{item}</View>
              </SwiperItem>
            );
          })}
        </Swiper>
      ) : (
        <View className="qz-swiperSearch__placeholder">输入职位名称搜索</View>
      )}
    </View>
  );
};
export default SwiperSearch;
