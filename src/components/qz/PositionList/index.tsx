import { FC } from "react";
import { View } from "@tarojs/components";
import Empty from "@components/Empty";
import NoMore from "@components/NoMore";
import PositionCard from "@components/PositionCard";
import "./index.scss";
import Taro from "@tarojs/taro";
import PositionImgCard from "@components/PositionImgCard";
import { useSelector } from "react-redux";

type PositionListProps = {
  data: PositionTypes[];
  scrollComplete: boolean;
  onChangeListData?: (id: number) => void;
};
const PositionList: FC<PositionListProps> = ({
  data,
  scrollComplete,
  onChangeListData
}) => {
  const {mode = 'list'} = useSelector((state:any) => state.list);
  const { TPL_CONFIG } = Taro.getApp().$app;
  const themeValue = TPL_CONFIG.theme.value;
  
  return (
    <View className={`qz-positionList qz-positionList-${themeValue}}`}>
      {data.length ? (
        <View className={`qz-positionList__inner qz-positionList__inner-${mode}`}>
          <View className="qz-positionList__list">
            {data.map(item =>{
              if(mode === 'list' ){
                return <PositionCard
                positionData={item}
                key={item.id}
                onChange={id => {
                  onChangeListData && onChangeListData(id);
                }}
              />
              }else{
                return <PositionImgCard
                positionData={item}
                key={item.id}
                onChange={id => {
                  onChangeListData && onChangeListData(id);
                }}
              />
              }
            })}
          </View>

          {/* {scrollComplete && <NoMore>没有更多数据了</NoMore>} */}
        </View>
      ) : (
        <Empty message="暂无数据" />
      )}
     
    </View>
  );
};
export default PositionList;
