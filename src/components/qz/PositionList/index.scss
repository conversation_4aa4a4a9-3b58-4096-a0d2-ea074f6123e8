@import '@styles/variables/default.scss';


.qz-positionList{
  &__inner-list{
    border-radius: 10px;
    overflow: hidden;
    background-color: #fff;
  }
  &__inner-card{
    position: relative;
    background-color: #f5f5f5;
    
    .qz-positionList__list{
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
  }
}
.qz-positionList-default.qz-positionList{
  .qz-positionList__inner-card{
    padding: 0 15px;
  }
    .qz-positionList__list{
      border-radius: 0px;
    }
}

#position-filter + .qz-positionList{
  .qz-positionList__inner-card{
    &::before  {
      content: '';
      top:0;
      left:0;
      width: 100%;
      height: 300px;
      display: inline-block;
      position: absolute;
      background-image: linear-gradient(to bottom, rgba(255,255,255) 5px, #f5f5f5);
    }
  }
}
