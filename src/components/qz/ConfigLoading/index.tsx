import Taro from '@tarojs/taro';
import { FC, useEffect } from 'react';
import eventWatch from '@utils/watch';
import Loading from '@components/Loading';
import { View } from '@tarojs/components';

type ConfigLoadingProps = {
  onInit: (values: any) => void;
}
const ConfigLoading: FC<ConfigLoadingProps> = ({ onInit }) => {
  const { SCREEN_WIDTH, MENU_BUTTON, TPL_CONFIG } = Taro.getApp().$app;
  const { right, bottom } = MENU_BUTTON;
  const NAV_GAP = SCREEN_WIDTH - right;
  const navbarHeight = bottom + NAV_GAP;

  useEffect(() => {
    if (TPL_CONFIG) {
      onInit(TPL_CONFIG);
    } else {
      eventWatch.one('CONFIG_READY', onInit, [true]);
    }
  }, [TPL_CONFIG, onInit]);

  return (
    <View style={{ paddingTop: navbarHeight }}>
      <Loading />
    </View>
  );
};
export default ConfigLoading;
