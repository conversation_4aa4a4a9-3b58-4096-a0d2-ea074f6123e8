import type { FC } from 'react'
import { useEffect,useState } from 'react'
import { container, ui } from "@landrover/business/index";
import { Block, ScrollView, View } from "@tarojs/components";
import './index.scss';
import PositionList from '../PositionList';
import { useDidShow } from '@tarojs/taro';

type ScrollViewIndexProps = {
  navData: {
    name: string
    cate: number
  };
  isRefresh?:boolean
};
const ScrollViewIndex: FC<ScrollViewIndexProps> = ({navData,isRefresh=false}) => {
  const [loading,setLoading] = useState<boolean>(true);
  const [scrollComplete, setScrollComplete] = useState<boolean>(false)
  const [pageIndex,setPageIndex] = useState<number>(1); 
  const [totalNumber,setTotalNumber] = useState<number>(0);
  const [listData,setListData] = useState<PositionTypes[]>([]);

  const defaultParams:any = {}
  if(navData?.cate) defaultParams.cate = navData.cate;
  if(!navData?.cate && navData?.name) defaultParams.keyword = navData.name;
  

  useEffect(()=>{
    if(isRefresh){
      setScrollComplete(false);
      getPositionList({
        current: 1
      });
    }
  },[isRefresh])

  useDidShow(()=>{
    if(isRefresh){
      setScrollComplete(false);
      getPositionList({
        current: 1
      });
    }
  })

  // useEffect(() => {
  //   getPositionList({
  //     current: 1,
  //   });
  // }, [])
  const getPositionList = (params,callback?:any)=>{
    container.saasService
    .getPositions({
      pageSize: 10,
      ...defaultParams,
      ...params,
    })
    .then((result: { list: any[]; current: number; total: number }) => {
     const {current,list,total}  = result;
      
     setLoading(false);
     setPageIndex(current);
     setListData(current===1?list:listData.concat(list));
     setTotalNumber(total);
     callback && callback();
    });
  }

  const onScrollToLower = () => {
    const getLastPageIndex = Math.ceil(totalNumber / 10);

    // 判断是否有下一页数据
    const hasNext = pageIndex < getLastPageIndex;
    // 没有下一页，无数加载更多数据
    if (!hasNext) {
      setScrollComplete(true);
      return;
    }

    const getPageIndex = pageIndex + 1;
    getPositionList({
      current: getPageIndex,
    });
  }
 
  const [refresherTriggered,setRefresherTriggered] = useState<boolean>(false)
  const onRefresherRefresh = ()=>{
    setRefresherTriggered(true)
    getPositionList({
      current: 1,
    },()=>{
      refreshCallback();
    });
  }

  const refreshCallback = ()=>{
    setTimeout(()=>{
      ui.showToast('职位已更新','none',1000)
      setRefresherTriggered(false)
    },500)
  }

  const handleUpdateListData = (id: number)=> {
    const data = [...listData];
    data.forEach(item => {
      if (item.id === id) {
        item.flow = {
          id
        };
      }
    });
    setListData(data);
  }


  return (
    <Block>
      <ScrollView
        className="qz-navContent"
        style={{
          position:'relative',
          height: '100%'
        }}
        refresherEnabled
        onRefresherRefresh={onRefresherRefresh}
        refresherBackground="#f5f5f5"
        refresherTriggered={refresherTriggered}
        scrollY
        scrollWithAnimation
        onScrollToLower={onScrollToLower}
      >
        <View className="qz-navContent__content">
          <PositionList onChangeListData={handleUpdateListData} data={listData} scrollComplete={scrollComplete} />
        </View>
      </ScrollView>
      </Block>
         
  );
};
export default ScrollViewIndex;
