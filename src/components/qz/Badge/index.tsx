import { View } from '@tarojs/components'
import type { FC } from 'react'
import './index.scss'

type BadgeProps = {
  count: number
  children: React.ReactNode
}
const Badge: FC<BadgeProps> = ({count,children}) => {
  if(!count) return <View>{children}</View>
  return (
    <View className="qz-Badge">
      {children}
      <View className="qz-Badge__count">{count}</View>
    </View>
  )
}
export default Badge