import { View } from '@tarojs/components'
import type { FC } from 'react'
import './index.scss'

type TextProps = {
  type: 'secondary' | 'warning' | 'danger' | 'success'
  children: React.ReactNode
  style?: React.CSSProperties
}
const Text: FC<TextProps> = ({type,children,style}) => {
  return (
    <View className={`qz-Text qz-Text__${type}`} style={style}>
      {children}
    </View>
  )
}
export default Text