import { ScrollView, View } from '@tarojs/components';
import React, { FC } from 'react';
import IconFont from '@components/iconfont';
import Taro from '@tarojs/taro';
import { ui } from '@landrover/foundation';
import { getMaterialUrl } from '@utils/index';
import './index.scss';

type CategoryProps = {
  style?: React.CSSProperties
};
const Category: FC<CategoryProps> = ({ style }) => {
  const { TPL_CONFIG: { category: { mode, list, iconType }, basic: { primaryColor } } } = Taro.getApp().$app;
  const menus = list.filter((item: any) => !(item.type && !item.isShow));

  const getIconFont = (item: any) => {
    if (iconType === 'single-color') {
      return <IconFont name={item.singleColorIcon} color={primaryColor} size={32} />;
    }
    if (iconType === 'double-color') {
      return <IconFont name={item.doubleColorIcon} size={32} />;
    }
    if (iconType === 'multi-color') {
      return <IconFont name={item.multiColorIcon} size={32} />;
    }
    return null;
  };

  const goTo = (item: any) => {
    const url = getMaterialUrl(item.link, item.text);

    // 金数据链接
    if (url.indexOf('jinshujuUrl') > -1) {
      const token = url.split('url=')[1];
      return Taro.navigateTo({ url: `/packageA/pages/jinshuju/index?token=${token}&title=${item.text}` });
    }

    // 第三方小程序链接
    if (url.indexOf('otherMiniappUrl') > -1) {
      const pageUrl = decodeURIComponent(url.split('url=')[1]);
      const [path, appId] = pageUrl.split('?appId=');
      return wx.navigateToMiniProgram({ appId, path });
    }

    // 内部链接
    if (url.indexOf('miniappUrl') > -1) {
      const pageUrl = decodeURIComponent(url.split('url=')[1]);
      return Taro.navigateTo({ url: pageUrl.startsWith('/') ? pageUrl : '/' + pageUrl });
    }

    if (item.text) {
      return Taro.navigateTo({ url });
    }

    return ui.showToast('无效的菜单链接');
  };

  if (mode === 'single-line') {
    const width = (menus.length > 5 ? 100 / 5.5 : 100 / 5) + '%';
    return <View className="qz-category qz-category__singleLine" style={style}>
      <ScrollView scrollX scrollWithAnimation className="qz-category__singleLine-scrollview">
        {menus.map((item: any) => <View onClick={() => goTo(item)} key={item.text} style={{ width }} className="qz-category__singleLine-item">
          {getIconFont(item)}
          <View className="qz-category__singleLine-item-text">{item.text || '-'}</View>
        </View>)}
      </ScrollView>
    </View>;
  }

  if (mode === 'double-line') {
    // 根据数量来计算列数
    const total = menus.length;
    const perLineCount = Math.ceil(menus.length / 2);
    const width = total > 5 ? (100 / perLineCount + '%') : (100 / total + '%');

    return <View className="qz-category qz-category__doubleLine">
      {menus.map((item: any) => <View onClick={() => goTo(item)} key={item.text} className="qz-category__doubleLine-item" style={{ width }}>
        {getIconFont(item)}
        <View className="qz-category__doubleLine-item-text">{item.text || '-'}</View>
      </View>)}
    </View>;
  }

  return null;
};
export default Category;
