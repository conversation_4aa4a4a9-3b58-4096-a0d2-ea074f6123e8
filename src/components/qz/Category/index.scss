.qz-category {
  width: 100%;
  padding: 10px 5px;
  border-radius: 10px;
  background-color: #fff;

  &__singleLine {
    &-scrollview {
      display: flex;
      white-space: nowrap;
    }

    &-item {
      display: inline-flex;
      align-items: center;
      flex-direction: column;
      gap: 2px;

      &-text {
        padding: 0 2px;
        text-align: center;
        font-size: 12px;
        white-space: break-spaces;
        color: #000;
      }
    }
  }

  &__doubleLine {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    &-item {
      display: flex;
      align-items: center;
      flex-direction: column;
      gap: 2px;

      &-text {
        padding: 0 2px;
        text-align: center;
        font-size: 12px;
        color: #000;
      }
    }
  }
}
