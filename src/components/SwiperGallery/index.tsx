import { Image, Swiper, SwiperItem, Video } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { formatOssUrl } from '@utils/format';
import { getFileSuffix } from '@utils/index';
import classNames from 'classnames';
import type { FC } from 'react'
import './index.scss'

type SwiperGalleryProps = {
  galleries: any[]
}
const SwiperGallery: FC<SwiperGalleryProps> = ({galleries=[]}) => {
  
  if(!galleries.length){
    galleries = [{
      url: 'https://oss.lingzhao.net/customer/clientGallery/1/1/bebe09a3-de9f-4c80-9bdd-d5a3114addeb.jpeg',
      type: 'image'
    }]
  };
  
  const sources:any[] = galleries.map((item)=>{
    const { url } = item
    const suffix = getFileSuffix(url)
    const isVideo = ['mp4', 'avi'].includes(suffix)
    return {
      url,
      type: isVideo ? 'video':'image'
    }
  })
  return (
    <Swiper
        className="qz-SwiperGallery__swiper"
        circular
        indicatorDots={galleries.length>1}
        interval={3000}
        duration={300}
        autoplay
    >
      {sources.map((item,index)=>{

        const {type} = item;
        return <SwiperItem key={item.url} className={classNames('qz-SwiperGallery__swiper-item',{
          'qz-SwiperGallery__swiper-video': type === 'video'
        })} onClick={()=>{
          Taro.previewMedia({
            current: index,
            sources // 需要预览的图片http链接列表
          })
        }}>
          {type === 'video' ?<Video
            style="border-radius: 10px;overflow:hidden;width:200px; height: 150px"
            objectFit="fill"
            src={item.url}
            // poster='https://misc.aotu.io/booxood/mobile-video/cover_900x500.jpg'
            initialTime={0}
            playBtnPosition="center"
            controls={true}
            autoplay={false}
            loop={false}
            muted={false}
          />:<Image
          className="qz-SwiperGallery__swiper-image"
          mode="aspectFill"
          src={formatOssUrl(item.url)}
        />}
        </SwiperItem>
        
      })}
    </Swiper>
  )
}
export default SwiperGallery