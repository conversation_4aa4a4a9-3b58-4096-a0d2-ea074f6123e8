@import '@styles/variables/default.scss';
@import '@styles/mixins/index.scss';

.qz-FormItem {
  width: 345px;
  margin: 0 auto;
  padding-top: 20px;
  @include hairline-bottom($color-border-shallow);

  &__label {
    font-size: 16px;
    font-weight: 400;
    color: #666;
  }

  &__body {
    @include display-flex();
    @include align-items(center);
    @include justify-content(space-between);
  }

  &__value {
    width:100%;
    padding:10px 0 20px;
    font-size: 16px;
    color: #2C2C2C;
  }

  &__placeholder {
    font-size: 16px;
    color: #ABABAB;
  }

  native-picker {
    flex: 1;
  }
}
