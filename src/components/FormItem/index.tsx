import { View } from '@tarojs/components';
import type { FC } from 'react';
import './index.scss';

type FormItemProps = {
  label: string
  required?: boolean
}
const FormItem: FC<FormItemProps> = ({ label, required = false, children }) => {
  return (
    <View className="qz-FormItem">
      <View className="qz-FormItem__label">{required ? <span style={{ display: 'inline', color: '#f00', marginRight: 2 }}>*</span> : ''}{label}</View>
      <View className="qz-FormItem__body">{children}</View>
    </View>
  );
};
export default FormItem;
