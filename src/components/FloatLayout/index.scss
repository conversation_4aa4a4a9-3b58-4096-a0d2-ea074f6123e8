.float-layout {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 90vh;
  overflow-y:auto;
  background-color: #FFF;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  // -webkit-transform: translate3d(0, 90%, 0);
  //         transform: translate3d(0, 90%, 0);
  -webkit-transition: -webkit-transform 300ms cubic-bezier(0.36, 0.66, 0.04, 1);
  transition: -webkit-transform 300ms cubic-bezier(0.36, 0.66, 0.04, 1);
  -o-transition: transform 300ms cubic-bezier(0.36, 0.66, 0.04, 1);
  transition: transform 300ms cubic-bezier(0.36, 0.66, 0.04, 1);
  transition: transform 300ms cubic-bezier(0.36, 0.66, 0.04, 1), -webkit-transform 300ms cubic-bezier(0.36, 0.66, 0.04, 1);
  &__trigger{
    width:100%;
    height: 24px;
    display: flex;
    justify-content: center;
    padding-top: 4px;
    span{
      display: inline-block;
      width: 80px;
      height: 6px;
      border-radius: 6px;
      background-color: #ddd;
    }
  }
}
