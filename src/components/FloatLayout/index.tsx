import { View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { FC, useEffect, useState } from "react";
import "./index.scss";

type FloatLayoutProps = {
  visible: boolean;
  children: React.ReactNode;
  onChangeDisableScroll: (value: boolean) => void;
};
const FloatLayout: FC<FloatLayoutProps> = ({
  visible,
  children,
  onChangeDisableScroll
}) => {
  const { SCREEN_HEIGHT } = Taro.getApp().$app;
  const [currentOffsetTop, setCurrentOffsetTop] = useState<number>(90);
  const [offsetTop, setOffsetTop] = useState<number>(90);
  const [startPageY, setStartPageY] = useState<number>(0);

  const showAll = () => {
    setOffsetTop(10);
    setCurrentOffsetTop(10);
  };
  const show = () => {
    setOffsetTop(90);
    setCurrentOffsetTop(90);
  };

  const hide = () => {
    setOffsetTop(100);
    setCurrentOffsetTop(100);
  };

  useEffect(() => {
    if (visible) {
      show();
    } else {
      hide();
    }
  }, [visible]);
  const handleTouchStart = e => {
    onChangeDisableScroll(true);
    setStartPageY(e.touches[0].pageY);
  };
  const handleTouchEnd = e => {
    onChangeDisableScroll(false);
    const triggerDistance = 50;
    const distance = startPageY - e.changedTouches[0].pageY;
    // 往上滑
    if (distance > triggerDistance) {
      showAll();
    }
    if (distance >= -triggerDistance && distance < 0) {
      showAll();
    }
    if (distance >= 0 && distance < triggerDistance) {
      show();
    }
    // 往下滑
    if (distance < -triggerDistance) {
      show();
    }
  };

  const handleTouchMove = e => {
    setOffsetTop(
      currentOffsetTop -
        ((startPageY - e.touches[0].pageY) * 200) / SCREEN_HEIGHT
    );
  };
  return (
    <View
      onTouchEnd={handleTouchEnd}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      className="float-layout"
      style={{
        "-webkit-transform": `translate3d(0, ${offsetTop}%, 0)`,
        transform: `translate3d(0, ${offsetTop}%, 0)`
      }}
    >
      <View className="float-layout__trigger">
        <span></span>
      </View>
      <View>{children}</View>
    </View>
  );
};
export default FloatLayout;
