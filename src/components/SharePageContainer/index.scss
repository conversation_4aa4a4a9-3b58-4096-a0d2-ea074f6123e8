@import '../../styles/variables/default.scss';

.qz-SharePageContainer{
  &__actions{
    padding: 25px 0 15px;
    display: flex;
    flex-direction:row;
    justify-content: center;
    align-items: center;
    &-item{
      border:solid ;
      border-width: 0;
      background-color: transparent;
      width: 100px;
      display: flex;
      flex-direction: column;
      align-items: center;
      &::after{
        border-width: 0;
      }
      &-text{
        font-size: 14px;
        color: $color-text-secondary;
      }
    }
  }
  &__btn{
    width: 100%;
    background-color: $color-bg-grey;
    color: $color-text-secondary;
    height:48px;
    line-height: 48px;
    text-align: center;
    padding-bottom: 16px;
    box-sizing: content-box;
  }
}