import { PageContainer, View } from '@tarojs/components'
import { useState, type FC } from 'react'
import { TaroCanvas } from "taro-canvas";
import IconFont from '@components/iconfont'
import RoundIcon from '@components/qz/RoundIcon'
import { ui,container } from "@landrover/business/index";
import { getPositionBaseInfo, getSalaryText } from '@utils/index';
import Taro from '@tarojs/taro';
import wxapi from '@utils/wxapp-promise'
import { useSelector } from 'react-redux'
import { stringify } from 'querystring';
import './index.scss'

type SharePageContainerProps = {
  visible
  onCancel:()=>void
  onChangeShareImg?:(url:string)=>void
}

const SharePageContainer: FC<SharePageContainerProps> = ({
  visible,
  onCancel,
  onChangeShareImg
}) => {
  const [copyStatus,setCopyStatus] = useState<'normal'|'copying'|'success'>('normal')
  const {dictMap={}} = useSelector((state:any) => state.dict);
  const shareData = useSelector(state => state.share);
  if(!shareData?.position?.id) return null;
  const {name,client,address,salaryType, salaryBegin, salaryEnd,city,workYears,degree,advantages,requirement,description} = shareData?.position || {};
  const salary = getSalaryText({salaryType, salaryBegin, salaryEnd},dictMap);
  const baseInfo = getPositionBaseInfo({ city, workYears, degree }, dictMap);
  const descInfo = requirement || description;


  let config: any = {
    width: 1500,
    height: 1200,
    backgroundColor: "#fff",
    debug: false,
    pixelRatio: 5,
    blocks: [
      {
        x: 0,
        y: 1000,
        width: 1500,
        height: 200,
        backgroundColor: 'linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #fff 100%)',
        // backgroundColor: "linear-gradient(to right, blue, pink)",
        zIndex: 99999
      }
    ],
    texts: [
      {
        x: 0,
        y: 40,
        text: salary,
        fontSize: 128,
        lineHeight: 160,
        color: "#FE3926",
        fontWeight: "bold",
        zIndex: 999
      },
      {
        x: 0,
        y: 224,
        text: baseInfo.map((item)=>item.value).join('·'),
        fontSize: 72,
        color: "#666666",
        lineHeight: 96,
        zIndex: 999
      },

      {
        x: 0,
        y: 536,
        text: '岗位详情',
        fontSize: 96,
        lineHeight: 120,
        color: "#000000",
        zIndex: 999
      },
      {
        x: -40,
        y: 680,
        text: '【应聘岗位】：',
        fontSize: 72,
        lineHeight: 88,
        color: "#000000",
        zIndex: 999
      },
      {
        x: 0,
        y: 800,
        width: 1400,
        text: descInfo,
        fontSize: 72,
        lineHeight: 104,
        color: "#333333",
        lineNum: 4,
        zIndex: 999
      },
    ],
    // images: [
    //   {
    //     url: imageBg,
    //     width: 1500,
    //     height: 200,
    //     y: 1000,
    //     x: 0,
    //     zIndex: 99999
    //   }
    // ]
  };


  advantages.forEach((item,index)=>{
    if(index<4){
      config.blocks.push({
        x: 0 + 344 * index,
        y: 360,
        width: 320,
        height: 112,
        backgroundColor: "#F3F3F3",
        borderRadius: 16,
      })
      config.texts.push({
        x: 0 + 344 * index,
        y: 380,
        width: 320,
        text: item.length > 4 ? (item.slice(0,4)+'...'):item,
        fontSize: 64,
        lineHeight: 112,
        color: "#666666",
        textAlign: "center",
        zIndex: 999
      })
    }

  })

  const onCreateSuccess = (result: {
    tempFilePath: string;
    errMsg: string;
  }): void => {
    const { tempFilePath, errMsg } = result;
    if (errMsg === "canvasToTempFilePath:ok") {
      // setShareImage(tempFilePath);
      onChangeShareImg && onChangeShareImg(tempFilePath);
      return;
    }
  };


  return (
    <View>
      <View style={{
        position:'absolute',
        left:'-99999px',
        top: 0,
      }}>
        <TaroCanvas
          config={config} // 绘制配置
          onCreateSuccess={result => {
            onCreateSuccess(result);
          }} // 绘制成功回调
          // onCreateFail={onCreateFail} // 绘制失败回调
        />

      </View>

      <PageContainer
        show={visible}
        position={'bottom'}
        onAfterLeave={() => {
          onCancel();
        }}
        round={true}>
        <View>
            <View className="qz-SharePageContainer__actions">
              <button open-type="share" className="qz-SharePageContainer__actions-item">
                <RoundIcon size={48} backgroundColor="#07C160">
                  <IconFont name="weixin" color="#fff" size={28} />
                </RoundIcon>
                <View  className="qz-SharePageContainer__actions-item-text" style={{
                  border:'none'
                }}>分享给好友</View>
              </button>
              <button className="qz-SharePageContainer__actions-item" onClick={()=>{
                Taro.navigateTo({
                  url: `/packageA/pages/share/position?id=${shareData?.id}`
                });
                // setIsFriendShare(false)
              }}>
                <RoundIcon size={48} backgroundColor="#07C160">
                  <IconFont name="pengyouquan" color="#fff" size={24} />
                </RoundIcon>
                <View className="qz-SharePageContainer__actions-item-text">生成海报</View>
              </button>
              <button className="qz-SharePageContainer__actions-item" onClick={()=>{
                setCopyStatus('copying')
                container.saasService.getSchemeUrl({
                  'path': '/packageA/pages/position/detail',
                  'query': stringify(shareData?.params) + `&sharedAt=${new Date().getTime()}`
                }).then((res) => {
                   wxapi
                  .setClipboardData({
                    data: `🔥🔥🔥${client.name}•${name}热招中\n💰💰💰【薪资待遇】${salary}\n🎉🎉🎉【任职要求】${requirement}\n🎉🎉🎉【职位描述】${description}\n📍📍📍【工作地点】${address}\n\n 👉👉👉${res.openlink}`
                  })
                  .then(() => {
                    ui.hideLoading()
                    setCopyStatus('success')
                    setTimeout(()=>{
                      setCopyStatus('normal')
                    },2000)
                  })
                });
              }}>
                {/* <Icon type="share_t" size={"48"}></Icon> */}
                <RoundIcon size={48} backgroundColor="#07C160">
                  <IconFont name="link" color="#fff" size={24} />
                </RoundIcon>
                 <View className="qz-SharePageContainer__actions-item-text">
                  {copyStatus === 'normal' && <View>复制链接</View>}
                  {copyStatus === 'copying' && <View>复制中...</View>}
                  {copyStatus === 'success' && <View style={{
                    color: '#07C160'
                  }}>复制成功</View>}
                </View>
              </button>
            </View>
            <View className="qz-SharePageContainer__btn" onClick={onCancel}>
              取消
            </View>
      </View>
      </PageContainer>
    </View>

  )
}
export default SharePageContainer
