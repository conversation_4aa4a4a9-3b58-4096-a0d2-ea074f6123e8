import Icon from "@components/Icon";
import IconFont from "@components/iconfont";
import RoundIcon from "@components/qz/RoundIcon";
import { View } from "@tarojs/components";
import Taro, { getCurrentPages } from "@tarojs/taro";
import { usePageScroll } from "@tarojs/taro";
import { ui } from "@landrover/business/index";
import { FC, useState } from "react";
import "./index.scss";

type CustomNavProps = {
  fav?: { id: number } | null;
  showLeftIcon?: boolean;
  showFavIcon?: boolean;
  showHomeIcon?: boolean;
  showBackIcon?: boolean;
  showShareIcon?: boolean;
  dynamicOpacity?: boolean;
  redirectUrl?: string;
  title?: string;
  onChangeShareVisible?: () => void;
  onChangeFavs?: () => void;
};
const roundIconStyle: React.CSSProperties = {
  border: "solid 0.5px #d7d7d7",
  backgroundColor: "#fff"
};
const CustomNav: FC<CustomNavProps> = ({
  fav,
  showShareIcon,
  showFavIcon,
  showLeftIcon = true,
  title,
  dynamicOpacity = true,
  onChangeShareVisible,
  onChangeFavs
}) => {
  const { SCREEN_WIDTH, MENU_BUTTON } = Taro.getApp().$app;
  const { top, right, left, bottom, height } = MENU_BUTTON;
  const NAV_GAP = SCREEN_WIDTH - right;
  const navbarHeight = bottom + NAV_GAP;
  const [opacity, setOpacity] = useState(0);

  usePageScroll(res => {
    if (dynamicOpacity) {
      const value = res.scrollTop / NAV_GAP;
      setOpacity(value > 1 ? 1 : value);
    }
  });

  const getLeftIcon = () => {
    const pages = getCurrentPages();
    const prePage = pages[pages.length - 2];

    return (
      <RoundIcon
        size={height}
        style={roundIconStyle}
        onClick={() => {
          if (prePage) {
            Taro.navigateBack();
          } else {
            Taro.switchTab({
              url: "/pages/index/index"
            });
          }
        }}
      >
        <Icon type={prePage ? "back" : "home"} size="20" />
      </RoundIcon>
    );
  };
  return (
    <View
      className="custom-nav"
      style={{
        paddingTop: top,
        // paddingLeft: NAV_GAP,
        height: navbarHeight,
        background: `rgba(255,255,255,${opacity})`,
        borderBottom: opacity === 1 ? "solid 0.5px #eee" : ""
      }}
    >
      {title && (
        <View
          style={{
            height,
            top: top
          }}
          className="custom-nav__title"
        >
          {title}
        </View>
      )}
      <View
        className="custom-nav__icons"
        style={{
          width: left,
          paddingLeft: NAV_GAP,
          paddingRight: NAV_GAP
        }}
      >
        {showLeftIcon ? getLeftIcon() : null}

        <View className="custom-nav__iconGroup">
          {showFavIcon && (
            <RoundIcon
              size={height}
              style={roundIconStyle}
              onClick={() => {
                onChangeFavs && onChangeFavs();
                ui.showToast(
                  fav?.id ? "取消收藏" : "收藏成功！可在个人中心查看"
                );
              }}
            >
              <IconFont
                name="like"
                color={fav?.id ? "#FE3925" : "#000000"}
                size={20}
              />
              {/* <Icon type="share" size="20" /> */}
            </RoundIcon>
          )}
          {showShareIcon && (
            <RoundIcon
              size={height}
              style={roundIconStyle}
              onClick={() => {
                onChangeShareVisible && onChangeShareVisible();
              }}
            >
              <IconFont name="fenxiang7" color={"#000000"} size={18} />
              {/* <Icon type="share" size="20" /> */}
            </RoundIcon>
          )}
        </View>
      </View>
    </View>
  );
};
export default CustomNav;
