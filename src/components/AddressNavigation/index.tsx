import QzButton from "@components/Button";
import IconFont from "@components/iconfont";
import { Block, View } from "@tarojs/components";
import { FC, useEffect, useState } from "react";
import './index.scss'

type AddressNavigationProps = {
  address: string;
};
var QQMapWX = require("@utils/qqmap");
var qqmapsdk = new QQMapWX({
  key: "MYZBZ-X26WG-4MYQG-IGSQN-HKUCS-O2FMU" // 必填
});

const AddressNavigation: FC<AddressNavigationProps> = ({ address }) => {
  const [location, setLocation] = useState<
    [number | undefined, number | undefined]
  >([undefined, undefined]);

  useEffect(() => {
    qqmapsdk.geocoder({
      address,
      success: function(res: any) {
        const newLocation = res.result.location;
        setLocation([newLocation.lat, newLocation.lng]);
      }
    });
  }, [address]);

  const getAddressCoordLocation = () => {
    if (!location[0] || !location[1]) return;
    wx.getLocation({
      type: "wgs84",
      success: () => {
        wx.openLocation({
          //​使用微信内置地图查看位置。
          latitude: location[0], //要去的纬度-地址
          longitude: location[1], //要去的经度-地址
          name: address,
          address
        });
      }
    });
  };

  return (
    <View className="qz-AddressNavigation">
      <View className="qz-AddressNavigation__address">{address}</View>
      {location[0] && location[1] && (
        <QzButton
          customStyle={{ width: "80px" }}
          onClick={() => {
            getAddressCoordLocation();
          }}
          size="small"
          type="gray"
          circle
        >
          <Block>
            <IconFont size={14} name="mp-daohang" color="#666" />
            导航
          </Block>
        </QzButton>
      )}
    </View>
  );
};
export default AddressNavigation;
