import { FC } from "react";
import Taro from "@tarojs/taro";
import { Block, View } from "@tarojs/components";
import QzButton from "@components/Button";
import DeliverButton from "@components/DeliverButton";
import { setPositionData } from '@stores/actions/position';
import PositionCardHeader from "@components/PositionCardHeader";
import CardFooter from "@components/CardFooter";
import { useDispatch } from 'react-redux';
import { setShareData } from '@stores/actions/share';
import PositionSalary from "@components/PositionSalary";
import PositionTags from "@components/PositionTags";
import "./index.scss";
import { getDiffTimeFromNow } from "@utils/format";


type Props = {
  style?: React.CSSProperties;
  positionData: PositionTypes;
  onChange?: (id: number) => void;
};
/**
 * 职位卡片
 * @param props
 */
const PositionCard: FC<Props> = ({ style, positionData, onChange }) => {
  const dispatch = useDispatch()
  const { TPL_CONFIG } = Taro.getApp().$app;
  const themeType = TPL_CONFIG.theme.value;
  const {
    id,
    flow,
    updatedAt
  } = positionData;


  function handleNavigateTo(url: string): void {
    Taro.navigateTo({
      url
    });
  }

  return (
    <View
      style={style}
      className="position-card"
      onClick={() =>{
        dispatch(setPositionData(positionData))
        handleNavigateTo(`/packageA/pages/position/detail?id=${id}`)}
      }
    >
      <View className="position-card__bd">
        <PositionCardHeader positionData={positionData} />
        <PositionSalary positionData={positionData} />
        <PositionTags positionData={positionData} />
      </View>

      <CardFooter
        desc={<View style={{
          fontWeight: '300'
        }}>
          {getDiffTimeFromNow(updatedAt)}更新
          {/* <AvatarGroup maxCount={4} /> */}
        </View>}
        action={[
          <QzButton
            circle
            fluid
            type="gray"
            size="small"
            onClick={() => {
              dispatch(setShareData({
                position:positionData,
                params:{
                  id: positionData.id
                },
                visible: true,
              }))
            }}
          >
            推荐好友
          </QzButton>,
          <Block>
            {flow?.id ? (
              <QzButton
                className="position-card__btn"
                circle
                fluid
                disabled
                size="small"
                type={themeType === "mt" ? "primary" : "secondary"}
              >
                已报名
              </QzButton>
            ) : (
              <DeliverButton
                className="position-card__btn"
                size="small"
                id={id}
                type={
                  ["mt", "verdant"].includes(themeType)
                    ? "primary"
                    : "secondary"
                }
                onSuccess={() => {
                  onChange && onChange(id);
                }}
              />
            )}
          </Block>
        ]}
      />
    </View>
  );
};

export default PositionCard;
