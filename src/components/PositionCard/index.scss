@import '@styles/variables/default.scss';
@import '@styles/mixins/index.scss';
.position-card:last-child{
  border-bottom: none
}
.position-card {
  background-color: #fff;
  overflow: hidden;
  padding: 15px;
  // margin-bottom: 10px;
  position: relative;
  width: 100%;
  border-bottom: solid 0.5px #eee;
  &__bd{
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding-bottom: 15px;
  }
  
  
  &__title {
    font-size: 16px;
    color: #262626;
    flex: 1;
    font-weight: 500;
    line-height: 20px;
    width: 100px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &__btn {
    width: 96px !important;
    padding: 0 !important;
  }
}
