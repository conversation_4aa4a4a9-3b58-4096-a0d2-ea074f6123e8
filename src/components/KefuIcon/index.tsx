import Icon from '@components/Icon'
import { View } from '@tarojs/components'
import type { FC } from 'react'
import { container } from "@landrover/business/index"
import './index.scss'
import Taro from '@tarojs/taro'


type KefuIconProps = {
  positionData:PositionTypes
}
const KefuIcon: FC<KefuIconProps> = ({positionData}) => {
  const {
    TPL_CONFIG,
  } = Taro.getApp().$app;
  const {primaryColor} = TPL_CONFIG.basic;
  const {id,name} = positionData;
  const chat = (e)=>{
    e.stopPropagation() // 阻止事件冒泡
    Taro.showLoading({title:'客服接入中...'})
    container.saasService.getKefuUrl(id).then((res) => {
      const {url} = res;
      if(url){
        wx.openCustomerServiceChat({
          extInfo: {url},
          corpId: 'ww672e97c0a5fb6f30',//tenant?.wecom?.openCorpId, // 墨黎科技:wx4a2b806082483a18  轻招科技:ww672e97c0a5fb6f30
          showMessageCard:true,
          sendMessageTitle:name,
          success: function (res) { }
        })
      };
    })
    
  }
  return (
    <button open-type="contact" onClick={ e=>{e.stopPropagation()}} className="qz-KefuIcon">
      <View className="qz-KefuIcon__content" style={{
        backgroundColor: primaryColor
      }}>
        <Icon type="message" size={'16'}></Icon>
      </View>
    </button>
  )
}
export default KefuIcon