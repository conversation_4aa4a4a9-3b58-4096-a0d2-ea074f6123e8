import Taro from "@tarojs/taro";
import { View } from "@tarojs/components";
import dayjs from "dayjs";
import Tag from "@components/Tag";
import Cell from "@components/Cell";
import Salary from "@components/Salary";
import QzButton from "@components/Button";
import { container, ui } from "@landrover/business/index";
import { getClientDisplayName } from "@utils/index";
import "./index.scss";
import { useSelector } from "react-redux";
import PositionSalary from "@components/PositionSalary";

type Props = {
  flowData: FlowTypes;
  onChange?: (id: number) => void;
};
const STATUS_MAP = {
  "10": {
    name: "报名成功",
    color: "green"
  }
};

const FlowCard: FixFunctionComponent<Props> = ({ flowData, onChange }) => {
  const {dictMap={}} = useSelector((state:any) => state.dict);
  const nodes = dictMap?.flow?.nodes;
  const { createdAt, currentNode,id,position } = flowData;
  function handleNavigateTo(url: string): void {
    Taro.navigateTo({
      url
    });
  }
  const {
    id:positionId,
    name,
  } = position;

  const onSubmit = () => {
    ui.showModal("提示", "确定取消本次报名吗", true, "取消", "确定").then(
      result => {
        if (result.confirm) {
          container.saasService.cancelDeliver(id).then(() => {
            onChange && onChange(id);
          });
        }
      }
    );
  };

  return (
    <View
      onClick={(): any => handleNavigateTo(`/packageA/pages/position/detail?id=${positionId}`)}
      className="qz-FlowCard"

    >
      <View>
        <View className="qz-FlowCard__name">{name}</View>
        <View className="qz-FlowCard__client">
          {getClientDisplayName(position)}
        </View>
      </View>
      <PositionSalary
        className="qz-FlowCard__salary"
        positionData={position as PositionTypes}
      />
      <Cell label="报名状态">
        <Tag color={STATUS_MAP[currentNode]?.color || "red"}>{`${
          STATUS_MAP[currentNode]
            ? STATUS_MAP[currentNode]?.name
            : "已" + nodes[Number(currentNode)]?.name
        }`}</Tag>
      </Cell>
      {createdAt && (
        <Cell label="报名时间">{dayjs(createdAt).format("YYYY-MM-DD")}</Cell>
      )}
      <QzButton
        className="qz-FlowCard__btn"
        circle
        fluid
        onClick={onSubmit}
        type="gray"
      >
        取消报名
      </QzButton>
    </View>
  );
};

export default FlowCard;
