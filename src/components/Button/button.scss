@import '@styles/variables/default.scss';
@import '@styles/mixins/index.scss';

$button-height-lg: 45px !default;
$button-height: 36px !default;
$button-height-sm: 30px !default;
$button-color: $color-brand !default;
$button-border-color-primary: $color-brand !default;
$button-border-color-secondary: $color-brand !default;
$button-bg: $button-color !default;
$gray-button-color: #d9d9d9;

qz-button {
  @include display-flex();
  @include align-items(center);
  @include justify-content(flex-start);
  width: fit-content;
}
.qui-web-button {
  border: 0;
  background: transparent;
  padding: 0;
  outline: none;
  box-shadow: none;
}

.qz-button {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  // width: 345px;
  padding: 0 $spacing-h-xl;
  height: $button-height;
  color: $color-text-base;
  font-size: $font-size-base;
  line-height: $button-height;
  text-align: center;
  border-radius: $border-radius-md;
  border: .5px solid #BBBBBB;
  box-sizing: border-box;
  &__noStyle{
    border:'none' !important;
    border-width: 0 !important;
    height: auto !important;
    margin:0;
    padding:0;
    background-color: transparent !important;
    font-size: 14px;
    line-height: inherit;
    &::after{
      border-width: 0;
    }
  }
  &__transparent{
    position: absolute;
    width: 100%;
    height: 100%;
    left:0;
    top:0;
    opacity: 0 !important;
  }
  @include line();

  &:active {
    opacity: $opacity-active;
  }

  /* elements */

  &__icon {
    display: inline-block;
    margin-right: 5px;
  }

  &__text {
    display: inline;
  }

  &__wxbutton {
    position: absolute;
    padding: 0;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    border: none;
    outline: none;
    background-color: transparent;
    opacity: 0;
    z-index: 1;

    &::after {
      display: none;
    }
  }

  /* modifiers */
  &--active {
    opacity: $opacity-active;
  }

  &--disabled {
    opacity: $opacity-disabled;

    &:active {
      opacity: $opacity-disabled;
    }
  }

  &--primary {
    color: $color-text-base-inverse;
    line-height: $button-height - 2;
  }
  // $button-color;
  &--secondary {
    background-color: $color-white;
  }

  &--gray{
    color: $gray-button-color;
    border: 1px solid $gray-button-color;;
  }

  &--circle {
    border-radius: $button-height / 2;
    background-clip: border-box;
    overflow: hidden;
  }
  &--large {
    padding: 0 $spacing-h-md;
    height: $button-height-lg;
    font-size: $font-size-lg;
    line-height: $button-height-lg - 2;

    &.qz-button--circle {
      border-radius: $button-height-lg / 2;
    }
    &.qz-button--fluid {
      display: inline-block;
      width: auto;
      padding: 0 20px;
    }
  }
  &--small {
    padding: 0 $spacing-h-md;
    height: $button-height-sm;
    font-size: $font-size-sm;
    line-height: $button-height-sm - 2;

    &.qz-button--circle {
      border-radius: $button-height-sm / 2;
    }
    &.qz-button--fluid {
      display: inline-block;
      width: auto;
      min-width: auto;
      padding: 0 15px;
    }
  }

  &--full {
    width: 375px;
    max-width: 100%;
    // border-radius: 0;
    // border-left: none;
    // border-right: none;
  }
  &--fluid {
    display: inline-block;
    width: auto;
    min-width: 100px;
    padding: 0 16px;
  }
}
.qz-button{
  
  &.mt,&.verdant{
    font-weight: 500;
    // &.qz-button--normal {
    //   color: #000;
    //   border: 2px solid #000;
    //   background: #fff;
    // }
    &.qz-button--primary {
      color: #000;
      border: 2px solid #000;
    }
    // $button-color;
    &.qz-button--secondary {
        color: #000;
        border: 2px solid #000;
        background: #fff;
    }
    
    &.qz-button--small {
      padding: 0 $spacing-h-lg;
      height: $button-height-sm;
      font-size: $font-size-sm;
      line-height: $button-height-sm - 2;
    }
  }
}

