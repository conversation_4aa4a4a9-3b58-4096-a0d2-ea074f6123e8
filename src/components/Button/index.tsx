import { View, Button } from '@tarojs/components'
import {ButtonProps} from '@tarojs/components/types'
import Taro from '@tarojs/taro'
import classNames from 'classnames'
import './button.scss'

const SIZE_CLASS = {
  large: 'large',
  normal: 'normal',
  small: 'small',
}

const TYPE_CLASS = {
  primary: 'primary',
  secondary: 'secondary',
}

export interface ButtonPropTypes {
  size?: 'normal' | 'large' | 'small'
  type?: 'primary' | 'secondary' | 'gray'
  circle?: boolean
  full?: boolean
  fluid?: boolean
  loading?: boolean
  disabled?: boolean
  onClick?: any
  customStyle?: React.CSSProperties
  formType?: 'submit' | 'reset' | undefined
  openType?:
    | 'contact'
    | 'share'
    | 'getUserInfo'
    | 'getPhoneNumber'
    | 'launchApp'
    | 'openSetting'
    | 'feedback'
    | 'getRealnameAuthInfo'
    | 'chooseAvatar'
    | undefined
  lang?: keyof ButtonProps.lang
  sessionFrom?: string
  sendMessageTitle?: string
  sendMessagePath?: string
  sendMessageImg?: string
  showMessageCard?: boolean
  appParameter?: string
  onGetUserInfo?: any
  onContact?: any
  onGetPhoneNumber?: any
  onError?: any
  onOpenSetting?: any
  children: import('react').ReactChild
  className?: string
  onChooseAvatar?: any
}

export default function QzButton(props: ButtonPropTypes): JSX.Element {

  const {
    TPL_CONFIG,
  } = Taro.getApp().$app
  

  const {
    circle,
    full,
    loading,
    fluid,
    disabled,
    
    formType,
    openType,
    lang,
    sessionFrom,
    sendMessageTitle,
    sendMessagePath,
    sendMessageImg,
    showMessageCard,
    appParameter,
    onGetUserInfo,
    onGetPhoneNumber,
    onOpenSetting,
    onError,
    onContact,
    onClick,
    children
  } = props

  let {type='',size='normal',customStyle={}}  = props
  const themeType = TPL_CONFIG.theme.value;
  const {primaryColor} = TPL_CONFIG.basic;
  const rootClassName = ['qz-button']
  if(['mt','verdant'].includes(themeType)) rootClassName.push(themeType);
  const classObject = {
    [`qz-button--${SIZE_CLASS[size]}`]: SIZE_CLASS[size],
    'qz-button--disabled': disabled,
    [`qz-button--${type}`]: TYPE_CLASS[type],
    'qz-button--circle': circle,
    'qz-button--full': full,
    'qz-button--fluid': fluid
  }

  function handleOnClick(e: any): any {
    if (!disabled && onClick) {
      e.stopPropagation()
      onClick && onClick(e)
    }
  }
  
  // 动态主题色
  const themeStyle:React.CSSProperties = {}
  if(type === 'primary'){
    if(!['mt','verdant'].includes(themeType)){
      themeStyle.border = `1px solid ${primaryColor}`;
    }
    themeStyle.background  = primaryColor;
  }
  if(type === 'secondary'){
    if(!['mt','verdant'].includes(themeType)){
      themeStyle.border = `1px solid ${primaryColor}`;
      themeStyle.color  = primaryColor;
    }
    
  }

  const mergeCustomStyle = {...themeStyle,...customStyle};

  return (
    <View
      className={classNames(props.className, rootClassName, classObject)}
      style={mergeCustomStyle}
      onClick={handleOnClick}>
      <Button
        className="qz-button__wxbutton"
        formType={formType}
        openType={openType}
        lang={lang}
        loading={loading}
        sessionFrom={sessionFrom}
        sendMessageTitle={sendMessageTitle}
        sendMessagePath={sendMessagePath}
        sendMessageImg={sendMessageImg}
        showMessageCard={showMessageCard}
        appParameter={appParameter}
        onGetUserInfo={onGetUserInfo}
        onGetPhoneNumber={onGetPhoneNumber}
        onOpenSetting={onOpenSetting}
        onError={onError}
        onContact={onContact}></Button>
      <View className="qz-button__text">{children}</View>
    </View>
  )
}

