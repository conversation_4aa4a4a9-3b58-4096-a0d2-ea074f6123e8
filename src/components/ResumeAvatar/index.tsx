import EuiIcon from '@components/Icon'
import { Image } from '@tarojs/components'
import type { FC } from 'react'
import './index.scss'

type ResumeAvatarProps = {
  url?: string
  gender?: number
  size?:string|number
}
const genderMap = {
  0: 'default_avatar',
  1: 'male',
  2: 'female',
};
const ResumeAvatar: FC<ResumeAvatarProps> = ({
  url,gender,size=80
}) => {
  const newUrl = url ? url : genderMap[(gender || 0)];
  if(url){
    return <Image style={`width: ${size}px; height: auto; border:solid 1px #f5f5f5; border-radius: 4px`} mode="widthFix" src={url} />
  }
  return (
    <EuiIcon className="qz-ResumeAvatar" type={newUrl} size={size}></EuiIcon>
  )
}
export default ResumeAvatar
