import { View } from '@tarojs/components';
import type { FC } from 'react';
import './index.scss';
import classNames from 'classnames';
import { useSelector } from 'react-redux';

type SalaryProps = {
  salaryType: number;
  salaryBegin: number;
  salaryEnd: number;
  className?: string;
};
const Salary: FC<SalaryProps> = ({ salaryType, salaryBegin, salaryEnd,className }) => {
  const {dictMap={}} = useSelector((state:any) => state.dict);
  const salaryTypeMap = dictMap?.position?.salaryType[salaryType];
  let value;
  let unit;
  // salaryType为6表示面议
  if (salaryType === 6 || !salaryTypeMap || (!salaryBegin && !salaryEnd)) {
    value = '面议';
  } else {
    value =
      salaryBegin.toString() +
      (salaryBegin && salaryEnd ? '-' : '') +
      salaryEnd.toString();
    unit = '元/' + salaryTypeMap.substring(0, 1);
  }
  return (
    <View className={classNames(className, "qz-salary")}>
      <View className='qz-salary__value'>{value}</View>
      {unit ? <View className="qz-salary__unit">{unit}</View> : null}
    </View>
  );
};
export default Salary;
