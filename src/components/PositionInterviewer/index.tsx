import Cell from '@components/Cell'
import Panel from '@components/Panel'
import { View } from '@tarojs/components'
import type { FC } from 'react'

type PositionInterviewerProps = {
  positionData: PositionTypes
}
const PositionInterviewer: FC<PositionInterviewerProps> = ({positionData}) => {
  const {interviewer,interviewTime,interviewAddress} = positionData;
  if(!interviewer && !interviewTime && !interviewAddress) return null;
  return (
    <Panel title="面试安排">
      <Cell label="联系人">
        {!!interviewer && <View style={{
          display: 'flex',
          gap: '4px'
        }}>
          <View>{interviewer?.name}</View>
          <View>{interviewer?.phone}</View>
        </View>}
      </Cell>
      {!!interviewTime && <Cell label="面试时间">{interviewTime}</Cell>}
      {!!interviewAddress && <Cell label="面试地点">{interviewAddress}</Cell>}
    </Panel>
  )
}
export default PositionInterviewer