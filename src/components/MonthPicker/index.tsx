import { Picker, View } from '@tarojs/components'
import { getYearAndMonthRange,MONTHS } from '@utils/index'
import classNames from 'classnames'
import { FC, useEffect, useState } from 'react'
import './index.scss'

type MonthPickerProps = {
  timeRange?: number[]
  soFar?: number
  name: string
  value?: string
  rangeKey?: string
  onChange?: (values)=>void;
}
const MonthPicker: FC<MonthPickerProps> = ({timeRange=[], soFar,name,value,rangeKey,onChange}) => {
  const data = getYearAndMonthRange((soFar !== undefined), timeRange);
  const placeholder = '请选择开始时间'
  const [displayValue, setDisplayValue] = useState<string>()
  const [pickerValue, setPickerValue] = useState<number[]>([])
  const [displayRange,setDisplayRange] = useState<any[]>(data);
  
 
  const handleChange = (e:any)=> {
    const result = e.detail.value
    if(displayRange[0][result[0]] === '至今'){
      if (onChange) onChange({
        [name]: '',
        soFar: 1
      })

     
    }else{
      const newDisplayValue  = displayRange[0][result[0]||0]+displayRange[1][result[1]||0]
      setDisplayValue(newDisplayValue)
      const formatValue = newDisplayValue.replace('年','-').replace('月','');
      if (onChange) onChange({
        [name]: formatValue,
        soFar: 0
      })
    }
    
  }

  const handleColumnChange = (e:any)=>{
    const {column,value} = e.detail;
    if(column === 0){
      if(displayRange[0][value] === '至今'){
        setDisplayRange([displayRange[0],[]]);
        setPickerValue([0,0])
      }else{
        if(!displayRange[1].length){
          setDisplayRange([displayRange[0],MONTHS]);
          setPickerValue([value,pickerValue[1]])
        }
      }
    }
  }

  useEffect(() => {
    if(soFar){
      setPickerValue([0,0])
      setDisplayValue('至今')
      setDisplayRange([displayRange[0],[]]);
    }else{
      if (value) {
        const index:number[] = [];
        const position = value.indexOf('年');
        const year = value.slice(0,position+1);
        const month = value.slice(position+1);
        
        index[0] = displayRange[0].findIndex((item)=>item == year);
        index[1] = displayRange[1].findIndex((item)=>item == month);
        
        setPickerValue(index)
        setDisplayValue(displayRange[0][index[0]]+displayRange[1][index[1]]);
      }
      if(soFar!==undefined && !value){
        setPickerValue([0,0])
        setDisplayRange([displayRange[0],[]]);
      }
    }
  }, [value,soFar])
  return (
    <View className="qz-MonthPicker">
      <Picker
        mode={'multiSelector'}
        name={name}
        value={pickerValue}
        rangeKey={rangeKey}
        range={displayRange}
        onChange={handleChange}
        onColumnChange={handleColumnChange}>
        <View
          className={classNames(['qz-MonthPicker__inner'], {
            placeholder: !displayValue
          })}>
          {displayValue ? displayValue : placeholder}
        </View>
      </Picker>
    </View>
  )
}
export default MonthPicker