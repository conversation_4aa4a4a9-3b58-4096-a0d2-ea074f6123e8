<?xml version="1.0" encoding="UTF-8"?>
<svg width="111px" height="110px" viewBox="0 0 111 110" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 61.2 (89653) - https://sketch.com -->
    <title>消息类@2x</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <circle id="path-1" cx="55.425535" cy="55" r="55"></circle>
        <linearGradient x1="0%" y1="31.9614236%" x2="50%" y2="31.9614236%" id="linearGradient-3">
            <stop stop-color="#92B9FF" offset="0%"></stop>
            <stop stop-color="#B8D1FF" offset="100%"></stop>
        </linearGradient>
        <rect id="path-4" x="0.340650485" y="0.882420077" width="9.702" height="40.964"></rect>
        <filter x="-100.9%" y="-90.6%" width="301.8%" height="281.2%" filterUnits="objectBoundingBox" id="filter-6">
            <feGaussianBlur stdDeviation="4" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="61.8201522%" y1="14.5594274%" x2="64.7662378%" y2="81.4273989%" id="linearGradient-7">
            <stop stop-color="#D0E3FF" offset="0%"></stop>
            <stop stop-color="#E0ECFF" offset="16.1910546%"></stop>
            <stop stop-color="#D0E2FF" offset="47.0228041%"></stop>
            <stop stop-color="#A9C7FD" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="41.2544799%" y1="57.1399277%" x2="63.4636121%" y2="60.4637686%" id="linearGradient-8">
            <stop stop-color="#9EC9FF" offset="0%"></stop>
            <stop stop-color="#79ABFF" offset="62.8666558%"></stop>
            <stop stop-color="#669DFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="56.8839104%" y1="59.4201952%" x2="92.3234867%" y2="39.8098308%" id="linearGradient-9">
            <stop stop-color="#8EB6FF" offset="0%"></stop>
            <stop stop-color="#5E98FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="70.9213858%" y1="50%" x2="29.0786142%" y2="56.0336252%" id="linearGradient-10">
            <stop stop-color="#C6DAFF" offset="0%"></stop>
            <stop stop-color="#AAC8FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="44.9428594%" y1="84.0790613%" x2="50%" y2="0%" id="linearGradient-11">
            <stop stop-color="#6DA1FF" offset="0%"></stop>
            <stop stop-color="#2F77FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <filter x="-27.8%" y="-8.9%" width="155.7%" height="117.7%" filterUnits="objectBoundingBox" id="filter-12">
            <feGaussianBlur stdDeviation="0.97826087" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="47.2698674%" y1="0%" x2="35.4222574%" y2="95.1457265%" id="linearGradient-13">
            <stop stop-color="#8DB6FF" offset="0%"></stop>
            <stop stop-color="#5B96FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="61.1001897%" y1="3.61766717%" x2="37.6937184%" y2="96.6564232%" id="linearGradient-14">
            <stop stop-color="#B8D1FF" offset="0%"></stop>
            <stop stop-color="#8AB4FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="79.7716929%" y1="3.61766717%" x2="16.9934617%" y2="96.6564232%" id="linearGradient-15">
            <stop stop-color="#B8D1FF" offset="0%"></stop>
            <stop stop-color="#8AB4FF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="定稿" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="缺省" transform="translate(-380.000000, -577.000000)">
            <g id="消息类" transform="translate(380.000000, 577.000000)">
                <g id="编组-2">
                    <mask id="mask-2" fill="white">
                        <use xlink:href="#path-1"></use>
                    </mask>
                    <use id="蒙版" fill="#F2F6FF" xlink:href="#path-1"></use>
                    <g mask="url(#mask-2)">
                        <g transform="translate(-8.000000, 14.000000)">
                            <g id="矩形" stroke-width="1" fill="none" transform="translate(58.425535, 70.751465)">
                                <mask id="mask-5" fill="white">
                                    <use xlink:href="#path-4"></use>
                                </mask>
                                <use id="蒙版" fill="url(#linearGradient-3)" xlink:href="#path-4"></use>
                                <polygon fill="#518FFF" filter="url(#filter-6)" mask="url(#mask-5)" points="-5.68434189e-14 1.24853516 11.8951998 4.88498131e-14 -5.68434189e-14 13.2485352"></polygon>
                            </g>
                            <path d="M50.9627919,73.7204045 C51.3811296,62.270752 52.3513162,50.4414063 51.2792947,39.7346516 C50.2072732,29.027897 45.5229473,22.4810455 36.4691141,22.1040039 C42.5046537,21.5659906 59.6555728,20.1896405 87.9218713,17.9749538 C96.2314851,17.3238911 103.49555,23.5323717 104.146615,31.8419853 C104.177344,32.2341864 104.192726,32.6274391 104.192726,33.0208423 L104.192726,65.6598981 C104.192726,66.755059 103.371685,67.6762315 102.283735,67.8017017 C85.1767538,69.7746026 68.0697729,71.7475035 50.9627919,73.7204045 Z" id="椭圆形备份" fill="url(#linearGradient-7)"></path>
                            <path d="M50.9237802,73.6967163 C50.3628416,56.2778255 55.9787576,26.0964355 37.9973153,22.3580207 C24.9764895,20.6694361 24.6220299,45.1263073 24.6481241,62.4926284 C24.6503814,63.9948734 50.9237802,73.6967163 50.9237802,73.6967163 Z" id="椭圆形" fill="url(#linearGradient-8)"></path>
                            <path d="M50.9237802,73.6967163 C50.9237802,73.6967163 51.0079833,68.9304199 51.1763895,59.3978271 L24.6470722,62.4165039 C42.1648775,69.9366455 50.9237802,73.6967163 50.9237802,73.6967163 Z" id="椭圆形备份-2" fill="url(#linearGradient-9)"></path>
                            <path d="M25.8393627,103.265903 C33.0345333,102.729782 41.8102139,94.6228557 41.0424808,84.2711441 C40.5306587,77.370003 39.6537587,68.7190641 38.4117806,58.3183272 L12,69.3959227 C12.6022418,75.6617362 12.9033627,80.6633962 12.9033627,84.4009027 C12.9033627,94.8197545 18.6441921,103.802024 25.8393627,103.265903 Z" id="椭圆形" fill="url(#linearGradient-10)" transform="translate(26.544776, 80.803531) rotate(46.000000) translate(-26.544776, -80.803531) "></path>
                            <path d="M61.713865,20.1191406 L67.5893533,19.5310059 L68.4681854,28.4874512 L61.2957504,47.997739 C63.2949406,51.1130141 64.7646286,52.6706516 65.7048143,52.6706516 C67.1150928,52.6706516 58.7248514,53.2526829 57.925535,45.6491523 C57.925535,44.1611867 58.8327209,42.0051867 60.6470928,39.1811523 L63.1968602,31.355957 L61.713865,20.1191406 Z" id="路径-18" fill="url(#linearGradient-11)" filter="url(#filter-12)"></path>
                            <path d="M80.0017975,4.35495262 L80.2621007,4.41458782 L87.9020369,9.6249567 C88.8655623,10.1985047 89.2285982,11.4148749 88.7370478,12.4227026 L87.2710019,15.6384197 C86.7796242,16.6458933 85.5982138,17.1087749 84.5532205,16.703258 L76.7088143,14.5242507 L77.8554716,15.0840679 L66.3227998,39.7637915 C69.6050881,40.074911 72.1728143,42.838919 72.1728143,46.2026516 C72.1728143,48.8991066 70.1180581,52.0812821 67.6284158,52.5655823 C67.1282632,52.662875 66.8551004,52.6706516 65.7048143,52.6706516 C62.1326365,52.6706516 59.2368143,49.7748294 59.2368143,46.2026516 C59.2368143,45.1827156 59.4728901,44.2179181 59.8933803,43.3599207 L59.8854719,43.3555913 L73.7428143,13.7002507 L73.7324567,13.6978165 L77.7568567,5.42919333 C78.1641138,4.59243375 79.0946937,4.14713609 80.0017975,4.35495262 Z" id="形状结合备份" fill="url(#linearGradient-13)"></path>
                            <path d="M81.1538975,4.77472832 C81.1916541,4.79310471 81.2286223,4.81305843 81.2647046,4.83453675 L89.3123154,9.6249567 C90.2758408,10.1985047 90.6388768,11.4148749 90.1473263,12.4227026 L88.1056854,16.6086869 C87.6143077,17.6161605 86.4328973,18.0790421 85.387904,17.6735252 L79.1810928,15.264 L67.7330783,39.7637915 C70.9395909,40.0677284 73.4641535,42.7126051 73.57901,45.9706652 L73.5830928,46.2026516 C73.5830928,49.7748294 70.6872706,52.6706516 67.1150928,52.6706516 C63.542915,52.6706516 60.6470928,49.7748294 60.6470928,46.2026516 C60.6470928,45.1823708 60.8833284,44.2172657 61.3040854,43.3590504 L61.2957504,43.3555913 L75.1530928,13.701 L75.1427352,13.6978165 L79.1487226,5.46702424 C79.5112642,4.72213824 80.4090115,4.4121868 81.1538975,4.77472832 Z" id="形状结合备份-2" fill="url(#linearGradient-14)"></path>
                            <path d="M66.5187356,49.1549683 C68.0375187,49.1549683 69.2687356,47.9237513 69.2687356,46.4049683 C69.2687356,44.8861852 68.0375187,43.6549683 66.5187356,43.6549683 C64.9999526,43.6549683 63.7687356,44.8861852 63.7687356,46.4049683 C63.7687356,47.9237513 64.9999526,49.1549683 66.5187356,49.1549683 Z" id="椭圆形备份-4" fill="#679DFF"></path>
                            <path d="M80.2478006,4.4152832 L80.8914316,4.66844177 C80.6581147,4.60958775 80.4600353,4.59979669 80.2971933,4.6390686 C79.905943,4.73342478 79.5225809,4.85559082 79.3096597,5.19766235 C79.3096597,5.19766235 73.4291564,17.649648 61.6681497,42.5536194 L78.9646127,5.19766235 C79.103783,4.92039998 79.2897724,4.71056112 79.5225809,4.56814575 C79.7553893,4.42573039 79.9971292,4.3747762 80.2478006,4.4152832 Z" id="路径-19" fill="#FFFFFF"></path>
                            <path d="M66.883909,48.7026516 C68.2646209,48.7026516 69.383909,47.5833635 69.383909,46.2026516 C69.383909,44.8219398 68.2646209,43.7026516 66.883909,43.7026516 C65.5031971,43.7026516 64.383909,44.8219398 64.383909,46.2026516 C64.383909,47.5833635 65.5031971,48.7026516 66.883909,48.7026516 Z" id="椭圆形" fill="url(#linearGradient-15)"></path>
                            <path d="M27.2494158,42.020328 C24.9138783,44.1089523 24.2380963,45.6507004 22.1302038,48.0982592 C20.0223113,50.5458179 15.3005449,52.4936419 18.5136971,52.9413137 C21.7268493,53.3889855 31.1910628,51.290246 33.8318373,46.1365793 C34.9353603,43.9829724 34.3750046,42.5457469 33.1782422,41.7546592 C33.0306338,41.6570867 34.0352957,40.155846 33.5146979,40.0103259 C32.9941001,39.8648057 32.2710277,41.3066191 32.0372595,41.2380519 C30.3995592,40.7576934 28.3338169,41.0505698 27.2494158,42.020328 Z" id="形状结合" fill="#72A9FF"></path>
                            <path d="M52.5157897,2.55409843 C50.592406,4.16073254 50.0358796,5.34669262 48.2999681,7.22943013 C46.5640566,9.11216764 42.6755432,10.6104938 45.3216685,10.9548567 C47.9677938,11.2992196 55.761852,9.68480465 57.9366075,5.72044558 C58.8453912,4.06382489 58.3839217,2.95826685 57.3983527,2.34973786 C57.2767929,2.27468205 58.1041615,1.11988157 57.6754339,1.00794298 C57.2467062,0.896004392 56.6512348,2.00509165 56.4587199,1.95234764 C55.1100255,1.58284104 53.4088259,1.8081306 52.5157897,2.55409843 Z" id="形状结合备份-3" fill="#72A9FF" opacity="0.325985863" transform="translate(51.425535, 6.000000) rotate(4.000000) translate(-51.425535, -6.000000) "></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>