import { Icon, Image, View } from '@tarojs/components'
import PropTypes from 'prop-types'
import classNames from 'classnames'

import './index.scss'

type Props = {
  type: string
  size?: string
  color: string
  className?: string
  circle?: boolean
  onClick?: (event: any) => any
}

const defaultTypes = [
  'success',
  'success_no_circle',
  'info',
  'warn',
  'waiting',
  'cancel',
  'download',
  'search',
  'clear'
]
const quicon = {
  icon_company: require('./images/icon_company.png'),
  icon_arrow: require('./images/Icon_arrow.png'),
  release: require('./images/release.png'),
  resume: require('./images/resume.png'),
  empty: require('./images/empty.svg'),
  pwd: require('./images/pwd.png'),
  share_timeline: require('./images/share_timeline.png'),
  share_friend: require('./images/share_friend.png'),
  logo_active: require('./images/logo_active.png'),
  miniapp: require('./images/miniapp.png'),
  home: require('./images/home.svg'),
  back: require('./images/back.svg'),
  message: require('./images/message.svg'),
  avatar_kefu: require('./images/avatar_kefu.png'),
  share: require('./images/share.svg'),
  close: require('./images/close.svg'),
  money: require('./images/money.png'),
  notice: require('./images/notice.svg'),
  privacy: require('./images/privacy.png'),
  quit: require('./images/quit.png'),
  male:require('./images/male.svg'),
  female:require('./images/female.svg'),
  default_avatar: require('./images/unknow.svg'),
  tag: require('./images/tag.png'),
}
export default function QzIcon(props: Props): JSX.Element {
  const style:React.CSSProperties = {};
  const { type, size, color, onClick,circle } = props
  const isTaroIcon = defaultTypes.some(item => item === type)
  if(circle){
    style.borderRadius = size+'px';
    style.overflow = 'hidden';
  }

  function handleClick(e): any {
    if (onClick) {
      onClick(e)
    }
  }
  return (
    <View
      className={classNames(props.className,['icon-wrap'])}
      onClick={handleClick}
      style={`width: ${size}px; height: ${size}px;`}>
      {isTaroIcon ? (
        <Icon
          type={type}
          size={size}
          style={{
            fontSize: `${size}px`,
            width: `${size}px`,
            height: `${size}px`,
            lineHeight: `${size}px`,
            ...style
          }}
          color={color}></Icon>
      ) : (
        <Image style={{
          width: `${size}px`,
          height: `${size}px`,
          ...style
        }} src={quicon[type]} />
      )}
    </View>
  )
}

QzIcon.defaultProps = {
  type: 'success',
  size: '20',
  color: ''
}

QzIcon.propTypes = {
  type: PropTypes.string,
  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  color: PropTypes.string,
  onClick: PropTypes.func,
  onTouchStart: PropTypes.func
}
