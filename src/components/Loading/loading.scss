@import '@styles/variables/default.scss';
@import '@styles/mixins/index.scss';

$loading-size: 18px !default;
$loading-color: $color-brand !default;

.stage {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
  margin: 0 -5%;
  overflow: hidden;
}
/**
 * ==============================================
 * Dot Flashing
 * ==============================================
 */
 $dot-size: 10px !default;
 $dot-color: $primary-color !default;
 .dot-flashing {
  position: relative;
  width: $dot-size;
  height: $dot-size;
  border-radius: 5px;
  background-color: $dot-color;
  color: $dot-color;
  animation: dotFlashing 1s infinite linear alternate;
  animation-delay: .5s;

  &::before,
  &::after  {
    content: '';
    display: inline-block;
    position: absolute;
    width: $dot-size;
    height: $dot-size;
    top: 0;
    border-radius: 5px;
    background-color: $dot-color;
    color: $dot-color;
  }

  &::before {
    left: -15px;
    animation: dotFlashing 1s infinite alternate;
    animation-delay: 0s;
  }

  &::after {
    left: 15px;
    animation: dotFlashing 1s infinite alternate;
    animation-delay: 1s;
  }
}


@keyframes dotFlashing {
  0% {
    background-color: $dot-color;
  }
  50%,
  100% {
    background-color: rgba(254,57,37,.2);
  }
}

