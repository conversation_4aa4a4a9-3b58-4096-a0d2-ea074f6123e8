import Tag from '@components/Tag'
import { View } from '@tarojs/components'
import type { FC } from 'react'
import './index.scss'

type PositionCoverTagsProps = {
  positionData: PositionTypes
}
const PositionCoverTags: FC<PositionCoverTagsProps> = ({positionData}) => {

  const {
    advantages,
  } = positionData;


  const style:React.CSSProperties  = {
    padding: '4px'
  }
  return (
    <View className="qz-PositionCoverTags">
      {advantages.slice(0, 2).map(item => {
          return (
            <Tag key={item} color="white" type="inverse" style={style}>
              {item}
            </Tag>
          );
        })}
      {advantages.length > 2 && <Tag color="white" type="inverse" style={style}>
      ···
            </Tag>}
    </View>
  )
}
export default PositionCoverTags