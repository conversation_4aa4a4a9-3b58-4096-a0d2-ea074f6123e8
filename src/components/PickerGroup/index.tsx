import AreaPicker from '@components/AreaPicker'
import DegreePicker from '@components/DegreePicker'
import SalaryPicker from '@components/SalaryPicker'
import { View } from '@tarojs/components'
import type { FC } from 'react'
import './index.scss'

type PickerGroupProps = {
  filterValues: any
  onChangeFilterValues: (values:any)=>void
}
const PickerGroup: FC<PickerGroupProps> = ({filterValues,onChangeFilterValues}) => {
  return (
    <View className="qz-PickerGroup">
      <View className="qz-PickerGroup__item">
          <AreaPicker filterValues={filterValues} onChangeFilterValues={onChangeFilterValues} />
        </View>
        <View className="qz-PickerGroup__item">
          <SalaryPicker filterValues={filterValues} onChangeFilterValues={onChangeFilterValues} />
        </View>
        <View className="qz-PickerGroup__item">
          <DegreePicker filterValues={filterValues} onChangeFilterValues={onChangeFilterValues} />
        </View>
        
    </View>
  )
}
export default PickerGroup