@import '@styles/variables/default.scss';
@import '@styles/mixins/index.scss';

.qz-PositionHeader{
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 8px;
  background-color: #fff;
  padding: 15px;
  &__row{
    display: flex;
    justify-content: space-between;
    align-items: center;
    &-title{
      font-size: 18px;
      line-height: 20px;
      color: #262626;
      font-weight: 500;
    }
    &-updatedAt{
      font-size: 13px;
      color: #999;
      font-weight: 300;
    }
  }
  
  &__track{
    font-size: 13px;
    display: inline-flex;
    align-items: center;
    height: 28px;
    gap: 4px;
    color: $color-text-secondary;
    &-deliveries{
      display: flex;
      gap: 2px;
      align-items: center;
      color: $color-text-secondary;
      font-size: 13px;
    }
  }
  
}