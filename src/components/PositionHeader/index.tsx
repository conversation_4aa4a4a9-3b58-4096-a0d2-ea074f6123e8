import AvatarGroup from '@components/AvatarGroup'
import IconFont from '@components/iconfont'
import PositionBaseInfo from '@components/PositionBaseInfo'
import PositionSalary from '@components/PositionSalary'
import { View } from '@tarojs/components'
import { getDiffTimeFromNow } from '@utils/format'
import type { FC } from 'react'
import { useSelector } from 'react-redux'
import './index.scss'


type PositionHeaderProps = {
  positionData: PositionTypes
  trackData: any
}
const PositionHeader: FC<PositionHeaderProps> = ({
  positionData,
  trackData
}) => {
  const {uv=0,deliveries=0,lastVisitorAvatars=[]} = trackData||{};
  const {mode = 'list'} = useSelector((state:any) => state.list);
  const {name,updatedAt} = positionData;
  return (
    <View className="qz-PositionHeader">
      <View className="qz-PositionHeader__row" >
        <View className="qz-PositionHeader__row-title">{name}</View>
        <View className="qz-PositionHeader__row-updatedAt">{getDiffTimeFromNow(updatedAt)}更新</View>
      </View>
      <PositionBaseInfo position={positionData} />
      <PositionSalary
        positionData={positionData}
      />
      {mode === 'list' && <View className='qz-PositionHeader__track'>
        <AvatarGroup data={lastVisitorAvatars} />
        <View>{(uv+4)}人浏览</View>
      </View>}
      {!!deliveries && <View className='qz-PositionHeader__track-deliveries'>
        <IconFont name="xuanzhong" color={"#389e0d"} size={16} />已报名{deliveries}人
      </View>}
      
    </View>
  )
}
export default PositionHeader