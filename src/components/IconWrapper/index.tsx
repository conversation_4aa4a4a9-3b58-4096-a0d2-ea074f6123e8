import { View } from '@tarojs/components'
import { FC } from 'react'
import './index.scss'
import Taro from '@tarojs/taro'


type IconWrapperProps = {
  type: string
  children: React.ReactNode
}
const IconWrapper: FC<IconWrapperProps> = ({type,children}) => {
  const {MENU_BUTTON,SCREEN_WIDTH} =Taro.getApp().$app;
  const {height,top,right} = MENU_BUTTON;
  return (
    <View className="qz-IconWrapper" style={{
      width: height+'px',
      height:  height+'px',
      top: top+"px",
      left: (SCREEN_WIDTH - right) + 'px'
    }}>
      {children}
    </View>
  )
}
export default IconWrapper