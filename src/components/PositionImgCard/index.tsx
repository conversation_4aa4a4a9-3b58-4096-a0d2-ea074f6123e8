import { FC } from "react";
import Taro from "@tarojs/taro";
import { Block, View } from "@tarojs/components";
import QzButton from "@components/Button";
import DeliverButton from "@components/DeliverButton";
import { setPositionData } from "@stores/actions/position";
import PositionCardHeader from "@components/PositionCardHeader";
import CardFooter from "@components/CardFooter";
import { useDispatch } from "react-redux";
import { setShareData } from "@stores/actions/share";
import PositionSalary from "@components/PositionSalary";
import "./index.scss";
import { getFileSuffix } from "@utils/index";
import PositionCoverImg from "@components/PositionCoverImg";
import PositionCoverTags from "@components/PositionCoverTags";
import { formatOssUrl } from "@utils/format";

type Props = {
  style?: React.CSSProperties;
  positionData: PositionTypes;
  onChange?: (id: number) => void;
};
/**
 * 职位卡片
 * @param props
 */
const PositionImgCard: FC<Props> = ({ style, positionData, onChange }) => {
  const dispatch = useDispatch();
  const { TPL_CONFIG } = Taro.getApp().$app;
  const themeType = TPL_CONFIG.theme.value;
  const { id, flow,client,advantages } = positionData;
  const galleries = [...positionData.galleries,...client.galleries];
  const imgGalleries:any[] = [];
  galleries.forEach((item)=>{
    const {url} = item;
    const suffix = getFileSuffix(url);
    const isVideo = ['mp4','avi'].includes(suffix);
    if(!isVideo){
      imgGalleries.push(item);
    }
  })
  const coverImg = imgGalleries?.length ? imgGalleries[0].url : "https://oss.lingzhao.net/customer/clientGallery/1/1/bebe09a3-de9f-4c80-9bdd-d5a3114addeb.jpeg";

  function handleNavigateTo(url: string): void {
    Taro.navigateTo({
      url
    });
  }

  return (
    <View
      style={style}
      className="qz-PositionImgCard"
      onClick={() => {
        dispatch(setPositionData(positionData));
        handleNavigateTo(`/packageA/pages/position/detail?id=${id}`);
      }}
    >
      <View style="position:relative;border-radius: 10px 10px 0 0">
        <PositionCoverImg  coverImg={formatOssUrl(coverImg)} />
        {!!advantages?.length && <PositionCoverTags positionData={positionData} />}
      </View>

      <View className="qz-PositionImgCard__bd">
        <View>
          <PositionCardHeader positionData={positionData} />
          <PositionSalary positionData={positionData} />

        </View>

        {/* <PositionTags positionData={positionData} /> */}
        <CardFooter
          action={[
            <QzButton
              circle
              fluid
              type="gray"
              className="qz-PositionImgCard__btn"
              size="small"
              onClick={() => {
                dispatch(
                  setShareData({
                    position: positionData,
                    params:{
                      id: positionData.id
                    },
                    visible: true
                  })
                );
              }}
            >
              推荐好友
            </QzButton>,
            <Block>
              {flow?.id ? (
                <QzButton
                  className="qz-PositionImgCard__btn"
                  circle
                  fluid
                  disabled
                  size="small"
                  type={themeType === "mt" ? "primary" : "secondary"}
                >
                  已报名
                </QzButton>
              ) : (
                <DeliverButton
                  className="qz-PositionImgCard__btn"
                  size="small"
                  id={id}
                  type={
                    ["mt", "verdant"].includes(themeType)
                      ? "primary"
                      : "secondary"
                  }
                  onSuccess={() => {
                    onChange && onChange(id);
                  }}
                />
              )}
            </Block>
          ]}
        />
      </View>
    </View>
  );
};

export default PositionImgCard;
