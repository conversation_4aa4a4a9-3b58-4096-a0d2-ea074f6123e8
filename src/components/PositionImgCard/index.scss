@import '@styles/variables/default.scss';
@import '@styles/mixins/index.scss';
.qz-PositionImgCard {
  background-color: #fff;
  overflow: hidden;
  margin-bottom: 10px;
  border-radius: 10px;
  position: relative;
  display: flex;
  flex-direction: column;
  width: 168px;
  &__cover{
    width: 100%;
    height: 167px;
    border-radius: 10px 10px 0 0;
    background-position: center center;
    background-size: auto 167px;
  }
  &__bd{
    display: flex;
    flex-direction: column;
    padding: 10px;
    gap: 8px;
    padding-bottom: 15px;
    flex: 1;
    justify-content: space-between;
  }
  &__btn {
    width: 72px !important;
    padding: 0 !important;
  }
}
