import { useState, useEffect } from "react";
import ReactDOM from "react-dom";
import Message, { MessageType } from "./message";
import { View } from "@tarojs/components";


export interface MessageApi {
  info: (text: string) => void;
  success: (text: string) => void;
  warning: (text: string) => void;
  error: (text: string) => void;
}

export interface Notice {
  text: string;
  key: string;
  type: MessageType;
}

let seed = 0;
const now = Date.now();
const getUuid = (): string => {
  const id = seed;
  seed += 1;
  return `MESSAGE_${now}_${id}`;
};

let add: (notice: Notice) => void;

export const MessageContainer = () => {
  const [notices, setNotices] = useState<Notice[]>([]);
  const timeout = 3 * 1000;
  const maxCount = 10;

  const remove = (notice: Notice) => {
    const { key } = notice;

    setNotices(prevNotices =>
      prevNotices.filter(({ key: itemKey }) => key !== itemKey)
    );
  };

  add = (notice: Notice) => {
    setNotices(prevNotices => [...prevNotices, notice]);

    setTimeout(() => {
      remove(notice);
    }, timeout);
  };

  useEffect(() => {
    if (notices.length > maxCount) {
      const [firstNotice] = notices;
      remove(firstNotice);
    }
  }, [notices]);

  return (
    <View className="message-container">
      {notices.map(({ text, key, type }) => (
        <Message key={key} type={type} text={text} />
      ))}
    </View>
  );
};

  let page = document.getElementById('xxx')
  let cell = document.createElement('div')
  cell.id = `message-wrapper`
  // page.appendChild(cell)

ReactDOM.render(<MessageContainer />, cell);

const api: MessageApi = {
  info: text => {
    add({
      text,
      key: getUuid(),
      type: "info"
    });
  },
  success: text => {
    add({
      text,
      key: getUuid(),
      type: "success"
    });
  },
  warning: text => {
    add({
      text,
      key: getUuid(),
      type: "warning"
    });
  },
  error: text => {
    add({
      text,
      key: getUuid(),
      type: "danger"
    });
  }
};

export default api;
