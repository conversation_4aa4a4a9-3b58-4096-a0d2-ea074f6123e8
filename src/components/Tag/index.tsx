import { View } from '@tarojs/components';
import classNames from 'classnames';
import type { FC } from 'react'
import './index.less'

type TagProps = {
  type?: string
  children:React.ReactNode,
  color?: string;
  className?:string;
  style?: React.CSSProperties;
}
const Tag: FC<TagProps> = ({type,children,color,className, style}) => {
  const tagClassName = classNames(
    'qz-tag',
    {
      [`qz-tag-${color}`]: color,
      [`qz-tag-${type}`]: type
    },
    className,
  );
  return (
    <View className={tagClassName} style={style}>
      {children}
    </View>
  )
}
export default Tag