import IconFont from '@components/iconfont';
import { Picker, View } from '@tarojs/components';
import { FC, useState } from 'react'
import './index.scss'

type DegreePickerProps = {
  filterValues: any
  onChangeFilterValues: (values:any)=>void
}
const DegreePicker: FC<DegreePickerProps> = ({filterValues,onChangeFilterValues}) => {
  const [degreeValue, setDegreeValue] = useState<any[]>([0]);
  const degreeOptions = [
    { id: "0", name: "不限"},
    { id: "1", name: "初中" },
    { id: "2", name: "职高/高中" },
    { id: "3", name: "中专/中技" },
    { id: "4", name: "专科" },
    { id: "5", name: "本科" },
    { id: "6", name: "硕士" },
    { id: "7", name: "博士" },
    { id: "8", name: "博士后" }
  ];
  return (
    <View className="qz-DegreePicker">
      <Picker
        mode="selector"
        rangeKey="name"
        range={degreeOptions}
        value={degreeValue}
        onChange={e => {
          const { value } = e.detail;
          onChangeFilterValues &&
            onChangeFilterValues({
              ...filterValues,
              degree:
                degreeOptions[value].id === "0"
                  ? undefined
                  : degreeOptions[value].id
            });
          setDegreeValue([value]);
        }}
      >
        <View className="qz-DegreePicker__value">
          <View className="qz-DegreePicker__value-text">
          {degreeValue[0] && degreeValue[0] !== "0"
            ? degreeOptions[degreeValue[0]]?.name
            : "学历要求"}
          </View>
          <IconFont name="arrowdownb" size={14} color="#999" />
        </View>
      </Picker>
    </View>
  )
}
export default DegreePicker