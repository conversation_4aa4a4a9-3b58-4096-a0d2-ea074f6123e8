@import '@styles/variables/default.scss';
@import '@styles/mixins/index.scss';

$qz-modal-width: 335px !default;
$qz-modal-header-text-color: $color-text-base !default;
$qz-modal-content-text-color: $color-text-base !default;
$qz-modal-btn-default-color: $color-text-base !default;
$qz-modal-btn-confirm-color: $color-brand !default;
$qz-modal-bg-color: $color-white !default;

$qz-modal-duration: 200ms;

.qz-modal {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  visibility: hidden;
  transition: visibility $qz-modal-duration ease-in;
  z-index: $zindex-modal;

  /* elements */
  &__overlay,
  &__container {
    opacity: 0;
    transition: opacity $qz-modal-duration ease-in;
  }

  &__overlay {
    @include overlay;
  }

  &__container {
    @include absolute-center();
    width: $qz-modal-width;
    border-radius: $border-radius-lg;
    background-color: $qz-modal-bg-color;
    overflow: hidden;
    padding: 30px 0;
  }

  &__header {
    color: $qz-modal-header-text-color;
    font-size: $font-size-xxl;
    line-height: $font-size-xxl;
    font-weight: bold;
    text-align: center;
  }

  &__content {
    padding: 0 20px;
    margin-top: 10px;
    max-height: 420px;
    color: $qz-modal-content-text-color;
    font-size: $font-size-base;
    box-sizing: border-box;
    text-align: center;
  }

  &__footer {


    .qz-modal__action {
      @include display-flex;
      gap: 8px;
      width: 270px;
      margin: 24px auto 0;
      & > .eui-button {
        @include flex(auto);

        margin-right: 10px;
        max-width: 130px;
        overflow: hidden;
        &:last-child {
          margin-right: 0;
        }
      }

      &--alone {
        justify-content: center;
      }
    }



    &--simple .qz-modal__action > eui-button:last-child:nth-child(2) {
      color: $qz-modal-btn-confirm-color;
    }
  }

  /* modifiers */
  &--active {
    visibility: visible;
    .qz-modal__overlay,
    .qz-modal__container {
      opacity: 1;
    }
  }
}
