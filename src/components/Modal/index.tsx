import React from "react";
import { View } from '@tarojs/components'
import classNames from 'classnames'
import _isFunction from 'lodash/isFunction'

import QzButton from '../Button'
import { handleTouchScroll } from '../../utils'

import './modal.scss'

type IProps = {
  isOpened: boolean
  closeOnClickOverlay: boolean
  customContainer: boolean
  onClose?: any
  onCancel?: any
  onConfirm?: any
  children: import('react').ReactChild
  title?: string
  cancelText?: string
  confirmText?: string
  isSimple?: boolean
}
type IState = {
  _isOpened: boolean

  isWEB?: boolean
}
export default class QzModal extends React.Component {
  props: IProps
  state: IState

  static externalClasses = ['header-class', 'container-class', 'content-class', 'action-class']
  static defaultProps = {
    closeOnClickOverlay: true,
    title: '',
    content: '',
    customContainer: false
  }
  constructor(props: IProps) {
    super(props)

    const { isOpened } = props
    this.state = {
      _isOpened: isOpened
    }
  }

  componentWillReceiveProps(nextProps): void {
    const { isOpened } = nextProps

    if (this.props.isOpened !== isOpened) {
      handleTouchScroll(isOpened)
    }

    if (isOpened !== this.state._isOpened) {
      this.setState({
        _isOpened: isOpened
      })
    }
  }

  handleClickOverlay = (): void => {
    if (this.props.closeOnClickOverlay) {
      this.setState(
        {
          _isOpened: false
        },
        this.handleClose
      )
    }
  }

  handleClose = (): void => {
    if (_isFunction(this.props.onClose)) {
      this.props.onClose()
    }
  }

  handleCancel = (): void => {
    if (_isFunction(this.props.onCancel)) {
      this.props.onCancel()
    }
  }

  handleConfirm = (): void => {
    if (_isFunction(this.props.onConfirm)) {
      this.props.onConfirm()
    }
  }

  handleTouchMove = (e): void => {
    e.stopPropagation()
  }

  render(): JSX.Element {
    const { _isOpened } = this.state
    const { title, cancelText, confirmText, customContainer, children } = this.props
    const rootClass = classNames('qz-modal', {
      'qz-modal--active': _isOpened
    })

    const isRenderAction = cancelText || confirmText

    return (
      <View className={rootClass}>
        <View onClick={this.handleClickOverlay} className="qz-modal__overlay" />
        <View className="qz-modal__container container-class">
          {title && <View className="qz-modal__header header-class">{title}</View>}

          {customContainer && children}
          {isRenderAction && (
            <View
              className={classNames('qz-modal__footer', {
                'qz-modal__footer--simple': this.props.isSimple
              })}>
              <View
                className={classNames('qz-modal__action', {
                  'qz-modal__action--alone': !cancelText || !confirmText
                })}>
                {cancelText && (
                  <QzButton type="secondary" circle onClick={this.handleCancel}>
                    {cancelText}
                  </QzButton>
                )}
                {confirmText && (
                  <QzButton type="primary" circle onClick={this.handleConfirm}>
                    {confirmText}
                  </QzButton>
                )}
              </View>
            </View>
          )}
        </View>
      </View>
    )
  }
}
