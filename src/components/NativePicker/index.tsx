import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import { View, Picker } from '@tarojs/components';
import { ageToDateString } from '@utils/format';
import './native-picker.scss';

type NativePickerProps = {
  className?: string
  mode?: string
  checked?: boolean
  disabled?: boolean
  onChange?: (value: any) => void
  onCancel?: () => void
  range?: any[]
  rangeKey?: string
  placeholder?: string
  value?: any
  start?: string
  end?: string
  fields?: string
  name?: string
  defaultBirthDate?: string
  fieldProps?: any
};

/**
 * 原生时间选择器.
 */
function NativePicker(props: NativePickerProps): JSX.Element {
  const { mode, value, onChange, range, rangeKey, placeholder, name, end, defaultBirthDate, fieldProps = {} } = props;
  const [selectorChecked, setSelectorChecked] = useState<any>();
  const [pickerValue, setPickerValue] = useState<any>(0);

  function handleChange(e: any): void {
    const result = mode === 'date' || mode === 'time' || mode === 'region' ? e.detail.value : range?.[e.detail.value];
    const text = mode === 'date' || mode === 'time' ? result : mode === 'region' ? result.join(' ') : result?.name;
    setSelectorChecked(text);

    if (onChange) onChange(mode === 'date' || mode === 'time' ? result : mode === 'region' ? result.join(' ') : (result || {}));
  }

  useEffect(() => {
    if ((value || value === 0) && range && mode !== 'date' && mode !== 'time') {
      const index = range.findIndex(item => item.id == value);
      setPickerValue(index);
      if (range[index]) setSelectorChecked(range[index].name);
    }

    if (mode === 'date' || mode === 'time') {
      if (value) {
        const text = typeof value === 'string' && value.indexOf('-') > -1 ? value : ageToDateString(+value);
        setPickerValue(text);
        setSelectorChecked(text);
      } else if (name === 'birth') {
        setPickerValue(defaultBirthDate);
      }
    }

    if (value && mode === 'region') {
      setPickerValue(value.split(' '));
      setSelectorChecked(value);
    }
  }, [value, range, mode, name, defaultBirthDate]);

  return (
    <View className={classNames(props.className, 'native-picker')}>
      <Picker
        mode={mode}
        name={name}
        value={pickerValue}
        rangeKey={rangeKey}
        end={end}
        range={range}
        onChange={handleChange}
        {...fieldProps}
      >
        <View className={classNames(['native-picker__inner'], { placeholder: !selectorChecked })}>
          {selectorChecked ? selectorChecked : placeholder}
        </View>
      </Picker>
    </View>
  );
}

NativePicker.defaultProps = {
  mode: 'selector',
  value: 0,
  range: [
    { id: 1, name: '美国' },
    { id: 2, name: '中国' },
    { id: 3, name: '巴西' },
    { id: 4, name: '日本' },
  ],
  rangeKey: 'name',
};
NativePicker.propTypes = {
  mode: PropTypes.string,
  disabled: PropTypes.bool,
  onChange: PropTypes.func,
  onCancel: PropTypes.func,
  range: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
  rangeKey: PropTypes.string,
  placeholder: PropTypes.string,
  value: PropTypes.any,
  start: PropTypes.string,
  end: PropTypes.string,
  fields: PropTypes.string,
  name: PropTypes.string,
};
export default NativePicker;
