import { Component } from 'react';
import { Provider } from 'react-redux';
import { container } from '@landrover/business/index';
import Taro, { getCurrentInstance } from '@tarojs/taro';
import { urlSearchToObject } from '@utils/format';
import { mergeTplConfig } from '@utils/index';
import RootContainer from '@components/RootContainer';
import eventWatch from './utils/watch';
import { checkUpdateVersion } from './utils';
import SAASService from './services/saas.service';
import USERService from './services/user.service';
import configStore from './stores';
import './app.scss';

const store = configStore();
container.saasService = new SAASService();
container.userService = new USERService();

class App extends Component {
  onLaunch() {
    checkUpdateVersion();
  }

  componentDidMount() {
    const that: any = this;
    that.extConfig = Taro.getExtConfigSync ? Taro.getExtConfigSync() : {};
    const { right, height } = that.MENU_BUTTON = wx.getMenuButtonBoundingClientRect();

    // 考虑到小程序有可能直接打开某个内页（比如职位详情），这里需要用同步方式先获取到相关变量
    try {
      const { screenHeight, screenWidth, statusBarHeight = 0 } = Taro.getSystemInfoSync();
      that.SCREEN_WIDTH = screenWidth;
      that.SCREEN_HEIGHT = screenHeight;
      that.STATUS_BAR_HEIGHT = statusBarHeight; // 系统状态栏高度
      that.NAV_GAP = screenWidth - right; // 导航栏两侧按钮间距
      that.NAV_BAR_HEIGHT = 44; // 导航栏高度
      that.NAV_BAR_WIDTH = screenWidth - that.NAV_GAP * 2; // 导航栏宽度
      that.LOGO_SIZE = height * 0.85; // 头部LOGO尺寸
    } catch (e) {
      // Do something when catch error
    }

    // 小程序账号信息
    that.ACCOUNT_INFO = wx.getAccountInfoSync();
  }

  getMpConfig() {
    const that: any = this;
    // 页面可能通过不同参数二维码进入，防止热更新参数不变，所以以下函数放在componentDidShow中
    const { router } = getCurrentInstance();
    const { scene } = router?.params || {};
    const sceneObj: any = scene ? urlSearchToObject(decodeURIComponent(scene)) : {};
    const { template } = sceneObj;
    container.saasService.getMpConfig().then(res => {
      if (!!template) {
        const newConfig = mergeTplConfig(template, res);
        that.TPL_CONFIG = newConfig;
        eventWatch.emit('CONFIG_READY', newConfig, true);
      } else {
        that.TPL_CONFIG = res;
        eventWatch.emit('CONFIG_READY', res, true);
      }
    });
  }

  wxLogin() {
    // 静默登录
    wx.login({
      success: res => {
        const { code } = res;
        if (code) {
          const invitor = Taro.getStorageSync('invitor');
          container.userService
          .getWxAuthLogin({
            code,
            invitor,
          })
          .then(() => this.getMpConfig())
          .catch(() => {
          });
        }
      },
      fail: () => {
      },
    });
  }

  componentDidShow() {
    const { router } = getCurrentInstance();
    const { scene } = router?.params || {};
    const sceneObj: any = scene
      ? urlSearchToObject(decodeURIComponent(scene))
      : {};
    const { s } = sceneObj;
    if (s) {
      Taro.setStorageSync('invitor', s);
    }

    this.wxLogin();
  }

  componentDidHide() {
  }

  componentDidCatchError() {
  }

  // 在 App 类中的 render() 函数没有实际作用
  // 请勿修改此函数
  render() {
    return (
      <Provider store={store}>
        <RootContainer>{this.props.children}</RootContainer>
      </Provider>
    );
  }
}

export default App;
