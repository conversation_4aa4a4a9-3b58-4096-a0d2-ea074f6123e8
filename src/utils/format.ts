import dayjs from 'dayjs'

/**
 * 年龄转化为日期
 * @param age
 */
 export function ageToDateString(age: number): string {
  const dateOfBirth = dayjs().subtract(age, 'year')

  return dateOfBirth.format('YYYY-MM-DD')
}

/**
* 格式化时间区间
* @param {string}
* @param {string}
* @param {0|1}
* @return {string}
*/
export const formatRangePicker = (startTime: string, endTime: string, soFar?: 0 | 1) => {
  const startTimeIsValid = startTime && dayjs(startTime).isValid();
  const endTimeIsValid = (endTime && dayjs(endTime).isValid());
  if (!(startTimeIsValid || endTimeIsValid || soFar)) return null;
  const newStartTime = startTimeIsValid ? dayjs(startTime).format('YYYY-MM') : '';
  const newEndTime = endTimeIsValid ? dayjs(endTime).format('YYYY-MM') : '';
  const time = newStartTime + ' - ' + (soFar === 1 ? '至今' : newEndTime);
  return time;
}

/**
* URL参数转化为对象
* @param {string}
*/
export const urlSearchToObject = (search) => {
  var result = search.replace(/&/g, '","').replace(/=/g, '":"');
  var reqDataString = '{"' + result + '"}';
  var obj = JSON.parse(reqDataString);
  return obj;
}


/**
* 获取相对时长
* @param {time} 时间
* @return {string}} 相对时长
*/
export const getDiffTimeFromNow = (time: string) => {
  const now = new Date();
  const days = dayjs(now).diff(time, 'days');
  const hours = dayjs(now).diff(time, 'hours');
  const minutes = dayjs(now).diff(time, 'minutes');
  const seconds = dayjs(now).diff(time, 'seconds');
  if (days) return days + '天前';
  if (hours) return hours + '小时前';
  if (minutes) return minutes + '分钟前';
  if (seconds) {
    if (seconds < 10) {
      return '刚刚';
    } else {
      return seconds + '秒前';
    }
  };
  return '刚刚';
}


/**
* 格式化oss图片路径
* @param {string} 图片路径
*/
export const formatOssUrl = (url:string) => {
  if(url.indexOf('oss.')>-1){
    return url + '?x-oss-process=image/resize,w_750';
  }
  return url;
}


