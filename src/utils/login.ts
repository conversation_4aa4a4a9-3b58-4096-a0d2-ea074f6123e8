import Taro from "@tarojs/taro";
import { container, ui } from '@landrover/business/index'

const updateUserProfilePromise = (res) => {

  return new Promise((resolve, reject) => {
    container.userService
      .updatePhone(res)
      .then(res => {
        resolve(res);
      })
      .catch((res) => {
        reject(res)
      });
  });
}

const getPhoneByCodePromise = (code: string) => {
  return new Promise((resolve, reject) => {
    container.userService
      .getPhoneByCode({
        code,
      })
      .then(res => {
        resolve(res);
        // updateUserProfile(res);
      })
      .catch((res) => {
        reject(res)
      });
  });

};

const getPhoneNumber = (e, callback) => {

  const { errMsg, code } = e.detail
  if (errMsg === 'getPhoneNumber:fail user deny') {
    ui.showToast('授权失败')
    return
  }
  if (errMsg !== 'getPhoneNumber:ok') {
    Taro.navigateTo({
      url: '/pages/login/index',
    })
    return
  }
  getPhoneByCodePromise(code).then(res => {
    return updateUserProfilePromise(res)
  }).then((res) => {
    callback && callback(res);
  }).catch((res)=>{
    ui.showToast(res.errorMessage)
  })

}

export { getPhoneNumber }