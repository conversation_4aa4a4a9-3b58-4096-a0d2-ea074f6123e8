const BASIC_DEFAULT = {
  title: {
    text: '',
    position: 'center',
  },
  primaryColor: '#FE3925',
  logo: {
    url: '',
    isShow: false,
  },
}
const BANNER_DEFAULT = {
  isShow: true,
  list: [
    {
      title: 'banner01.png',
      link: '',
      image: 'https://oss-dev.lingzhao.net/common/banner/banner01.png',
    },
    {
      title: 'banner02.png',
      link: '',
      image: 'https://oss-dev.lingzhao.net/common/banner/banner02.png',
    },
  ],
}
const NOTICE_DEFAULT = {
  isShow: true,
  list: [
    {
      title: '连续签到第3天，连续最高200积分',
      button: {
        text: '去签到',
      },
      link: '',
    },
    {
      title: '加微信群，获取更多好工作',
      button: {
        text: '去加群',
      },
      link: '',
    },
    {
      title: '成功邀请好友报名即可获得百元红包',
      button: {
        text: '去邀请',
      },
      link: '',
    },
  ],
}
const SEARCH_DEFAULT = {
  isSwiper: true,
  list: ['快递员', '行政专员', '驾驶员'],
}

const TPLS_MAP = {
  default: {
    basic: BASIC_DEFAULT,
    theme: {
      value: 'default',
    },
    search: SEARCH_DEFAULT,
    banner: BANNER_DEFAULT,
    category: {
      isShow: true,
      mode: 'single-line', // single-line:单行模式 double-line:多行模式
      iconType: 'double-color',
      // color: '#FE3925', // 图标颜色
      list: [
        {
          singleColorIcon: 'a-miaosha',
          doubleColorIcon: 'shenghuojiaofei',
          multiColorIcon: 'gonglve1',
          text: '急招',
          link: '/position?isTop=1',
        },
        {
          singleColorIcon: 'huore',
          doubleColorIcon: 'huore1',
          multiColorIcon: 'dianzan3',
          text: '热招',
          link: '/position?isHot=1',
        },
        {
          singleColorIcon: 'qiandao',
          doubleColorIcon: 'renzheng',
          multiColorIcon: 'jiaoxue',
          text: '全职',
          link: '/position?workType=1',
        },
        {
          singleColorIcon: 'a-youhuiquan',
          doubleColorIcon: 'daishenpi',
          multiColorIcon: 'luxiang1',
          text: '兼职',
          link: '/position?workType=2',
        },
        {
          singleColorIcon: 'xihuan',
          doubleColorIcon: 'xueli',
          multiColorIcon: 'xiaoxi3',
          text: '实习',
          link: '/position?workType=3',
        },
        {
          singleColorIcon: 'kefu1',
          doubleColorIcon: 'biaoqingbao1',
          multiColorIcon: 'pinglun-08',
          text: '客服',
          link: '/position?cate=2',
        },
        {
          singleColorIcon: 'a-xunzhang',
          doubleColorIcon: 'huiyuan2',
          multiColorIcon: 'xunzhang3',
          text: '销售',
          link: '/position?cate=1',
        },
        {
          singleColorIcon: 'a-daifahuo',
          doubleColorIcon: 'wode2',
          multiColorIcon: 'yunshu',
          text: '驾驶员',
          link: '/position?cate=18',
        },
        {
          singleColorIcon: 'shangpu',
          doubleColorIcon: 'xueli',
          multiColorIcon: 'women2',
          text: '保安',
          link: '/position?cate=874',
        },
        {
          text: '',
          link: '',
        },
      ],
    },
    notice: NOTICE_DEFAULT,
  },
  mt: {
    basic: {
      ...BASIC_DEFAULT,
      primaryColor: '#FFD84A',
    },
    theme: {
      value: 'mt',
    },
    search: SEARCH_DEFAULT,
    banner: BANNER_DEFAULT,
    category: {
      isShow: true,
      mode: 'double-line', // single-line:单行模式 double-line:多行模式
      iconType: 'double-color', // colour:彩色风格 outline:线框风格 fill:实底模式
      // color: '#FE3925', // 图标颜色
      list: [
        {
          singleColorIcon: 'a-miaosha',
          doubleColorIcon: 'a-21tucao',
          multiColorIcon: 'gonglve1',
          text: '急招',
          link: '/position?isTop=1',
        },
        {
          singleColorIcon: 'huore',
          doubleColorIcon: 'a-14naozhong',
          multiColorIcon: 'hongbao3',
          text: '热招',
          link: '/position?isHot=1',
        },
        {
          singleColorIcon: 'qiandao',
          doubleColorIcon: 'a-25wode2',
          multiColorIcon: 'jiaoxue',
          text: '全职',
          link: '/position?workType=1',
        },
        {
          singleColorIcon: 'a-youhuiquan',
          doubleColorIcon: 'a-24wode1',
          multiColorIcon: 'luxiang1',
          text: '兼职',
          link: '/position?workType=2',
        },
        {
          singleColorIcon: 'xihuan',
          doubleColorIcon: 'a-26wode3',
          multiColorIcon: 'xiaoxi3',
          text: '实习',
          link: '/position?workType=3',
        },
        {
          singleColorIcon: 'kefu1',
          doubleColorIcon: 'a-08kefu',
          multiColorIcon: 'pinglun-08',
          text: '客服',
          link: '/position?cate=2',
        },
        {
          singleColorIcon: 'a-xunzhang',
          doubleColorIcon: 'a-13shezhi',
          multiColorIcon: 'xunzhang3',
          text: '销售',
          link: '/position?cate=1',
        },
        {
          singleColorIcon: 'a-daifahuo',
          doubleColorIcon: 'a-23',
          multiColorIcon: 'yunshu',
          text: '驾驶员',
          link: '/position?cate=18',
        },
        {
          singleColorIcon: 'shangpu',
          doubleColorIcon: 'a-31shouye3',
          multiColorIcon: 'women2',
          text: '保安',
          link: '/position?cate=874',
        },
        {
          text: '',
          link: '',
        },
      ],
    },
    notice: NOTICE_DEFAULT,
  },
  verdant: {
    basic: {
      ...BASIC_DEFAULT,
      primaryColor: '#3FE699',
    },
    theme: {
      value: 'verdant',
    },
    search: SEARCH_DEFAULT,
    banner: BANNER_DEFAULT,
    category: {
      isShow: true,
      mode: 'double-line', // single-line:单行模式 double-line:多行模式
      iconType: 'double-color', // colour:彩色风格 outline:线框风格 fill:实底模式
      // color: '#FE3925', // 图标颜色
      list:  [
        {
          singleColorIcon: 'a-miaosha',
          doubleColorIcon: 'kuaisu',
          multiColorIcon: 'gonglve1',
          text: '急招',
          link: '/position?isTop=1',
        },
        {
          singleColorIcon: 'huore',
          doubleColorIcon: 'xihuan1',
          multiColorIcon: 'hongbao3',
          text: '热招',
          link: '/position?isHot=1',
        },
        {
          singleColorIcon: 'qiandao',
          doubleColorIcon: 'shijian1',
          multiColorIcon: 'jiaoxue',
          text: '全职',
          link: '/position?workType=1',
        },
        {
          singleColorIcon: 'a-youhuiquan',
          doubleColorIcon: 'jiage',
          multiColorIcon: 'luxiang1',
          text: '兼职',
          link: '/position?workType=2',
        },
        {
          singleColorIcon: 'xihuan',
          doubleColorIcon: 'b-bianji',
          multiColorIcon: 'xiaoxi3',
          text: '实习',
          link: '/position?workType=3',
        },
        {
          singleColorIcon: 'kefu1',
          doubleColorIcon: 'gerenzhongxin',
          multiColorIcon: 'pinglun-08',
          text: '客服',
          link: '/position?cate=2',
        },
        {
          singleColorIcon: 'a-xunzhang',
          doubleColorIcon: 'jiangbei',
          multiColorIcon: 'xunzhang3',
          text: '销售',
          link: '/position?cate=1',
        },
        {
          singleColorIcon: 'a-daifahuo',
          doubleColorIcon: 'kuaidi',
          multiColorIcon: 'yunshu',
          text: '驾驶员',
          link: '/position?cate=18',
        },
        {
          singleColorIcon: 'shangpu',
          doubleColorIcon: 'zhuye1',
          multiColorIcon: 'women2',
          text: '保安',
          link: '/position?cate=874',
        },
        {
          text: '',
          link: '',
        },
      ],
    },
    notice: NOTICE_DEFAULT,
  },
  classic: {
    basic: {
      ...BASIC_DEFAULT,
      primaryColor: '#FC953D',
    },
    theme: {
      value: 'classic',
    },
    search: SEARCH_DEFAULT,
    banner: BANNER_DEFAULT,
    category: {
      isShow: true,
      mode: 'double-line', // single-line:单行模式 double-line:多行模式
      iconType: 'multi-color', // colour:彩色风格 outline:线框风格 fill:实底模式
      // color: '#FE3925', // 图标颜色
      list: [
        {
          singleColorIcon: 'a-miaosha',
          doubleColorIcon: 'shenghuojiaofei',
          multiColorIcon: 'gonglve1',
          type: 'isTop',
          isShow: true,
          text: '急招',
          link: '/position?isTop=1',
        },
        {
          singleColorIcon: 'huore',
          doubleColorIcon: 'huore1',
          multiColorIcon: 'hongbao3',
          text: '热招',
          link: '/position?isHot=1',
        },
        {
          singleColorIcon: 'qiandao',
          doubleColorIcon: 'renzheng',
          multiColorIcon: 'jiaoxue',
          text: '全职',
          link: '/position?workType=1',
        },
        {
          singleColorIcon: 'a-youhuiquan',
          doubleColorIcon: 'daishenpi',
          multiColorIcon: 'luxiang1',
          text: '兼职',
          link: '/position?workType=2',
        },
        {
          singleColorIcon: 'xihuan',
          doubleColorIcon: 'xueli',
          multiColorIcon: 'xiaoxi3',
          text: '实习',
          link: '/position?workType=3',
        },
        {
          singleColorIcon: 'kefu1',
          doubleColorIcon: 'biaoqingbao1',
          multiColorIcon: 'pinglun-08',
          text: '客服',
          link: '/position?cate=2',
        },
        {
          singleColorIcon: 'a-xunzhang',
          doubleColorIcon: 'huiyuan2',
          multiColorIcon: 'xunzhang3',
          text: '销售',
          link: '/position?cate=1',
        },
        {
          singleColorIcon: 'a-daifahuo',
          doubleColorIcon: 'wode2',
          multiColorIcon: 'yunshu',
          text: '驾驶员',
          link: '/position?cate=18',
        },
        {
          singleColorIcon: 'shangpu',
          doubleColorIcon: 'xueli',
          multiColorIcon: 'women2',
          text: '保安',
          link: '/position?cate=874',
        },
        {
          text: '',
          link: '',
        },
      ],
    },
    notice: NOTICE_DEFAULT,
  },
  xmly: {
    basic: {
      ...BASIC_DEFAULT,
      primaryColor: '#FF623E',
    },
    theme: {
      value: 'xmly',
    },
    search: SEARCH_DEFAULT,
    nav: {
      isShow: true,
      list: [
        {
          name: '全部',
          value: 'recommend',
        },
        {
          name: '文员',
          cate: 3,
        },
        {
          name: '销售',
          cate: 1,
        },
        {
          name: '保洁',
          cate: 17,
        },
        {
          name: '客服',
          cate: 2,
        },
      ],
    },
    banner: BANNER_DEFAULT,
    category: {
      isShow: true,
      mode: 'double-line', // single-line:单行模式 double-line:多行模式
      iconType: 'single-color', // colour:彩色风格 outline:线框风格 fill:实底模式
      // color: '#FE3925', // 图标颜色
      list: [
        {
          singleColorIcon: 'a-miaosha',
          doubleColorIcon: 'shenghuojiaofei',
          multiColorIcon: 'gonglve1',
          text: '急招',
          link: '/position?isTop=1',
        },
        {
          singleColorIcon: 'huore',
          doubleColorIcon: 'huore1',
          multiColorIcon: 'hongbao3',
          text: '热招',
          link: '/position?isHot=1',
        },
        {
          singleColorIcon: 'qiandao',
          doubleColorIcon: 'renzheng',
          multiColorIcon: 'jiaoxue',
          text: '全职',
          link: '/position?workType=1',
        },
        {
          singleColorIcon: 'a-youhuiquan',
          doubleColorIcon: 'daishenpi',
          multiColorIcon: 'luxiang1',
          text: '兼职',
          link: '/position?workType=2',
        },
        {
          singleColorIcon: 'xihuan',
          doubleColorIcon: 'xueli',
          multiColorIcon: 'xiaoxi3',
          text: '实习',
          link: '/position?workType=3',
        },
        {
          singleColorIcon: 'a-wode',
          doubleColorIcon: 'biaoqingbao1',
          multiColorIcon: 'pinglun-08',
          text: '客服',
          link: '/position?cate=2',
        },
        {
          singleColorIcon: 'a-xunzhang',
          doubleColorIcon: 'huiyuan2',
          multiColorIcon: 'xunzhang3',
          text: '销售',
          link: '/position?cate=1',
        },
        {
          singleColorIcon: 'a-dianzan',
          doubleColorIcon: 'women1',
          multiColorIcon: 'aixin',
          text: '保洁',
          link: '/position?cate=944',
        },
        {
          singleColorIcon: 'a-daifahuo',
          doubleColorIcon: 'wode2',
          multiColorIcon: 'yunshu',
          text: '驾驶员',
          link: '/position?cate=18',
        },
        {
          text: '',
          link: '',
        },

      ],
    },
    notice: NOTICE_DEFAULT,
  },
};

const TEMPLATE_INFO = [
  {
    value: 'default',
    title: '简约风格',
    alias: '简约',
    desc: '适合职位数量少，职位类型少的的官网类型',
    color: '#FE3925'
  },
  {
    value: 'mt',
    title: '美团风格',
    alias: '美团',
    desc: '美团橙底黑边风格，稳重又不失活泼',
    color: '#FFD84A'
  },
  {
    value: 'verdant',
    title: '翠绿风格',
    alias: '翠绿',
    desc: '一款清雅亮丽的风格',
    color: '#3FE699'
  },
  {
    value: 'classic',
    title: '经典风格',
    alias: '经典',
    desc: '经典的布局类型模版，美观大气',
    color: '#FC953D'
  },
  {
    value: 'xmly',
    title: '平台风格',
    alias: '平台',
    desc: '适合职位类型多，数量多的地方性平台模式',
    color: '#FE3925'
  }
]


export { TPLS_MAP, TEMPLATE_INFO }

