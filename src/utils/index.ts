// @flow
import Taro, { getCurrentPages } from '@tarojs/taro';
import dayjs from 'dayjs';
import CryptoJS from 'crypto-js';
import { TPLS_MAP } from './data';

const ENV = Taro.getEnv();

function handleTouchScroll(flag: boolean) {
  if (ENV !== Taro.ENV_TYPE.WEB) {
    return;
  }
  if (flag) {
    const scrollTop = document.documentElement.scrollTop;
    // 使body脱离文档流
    document.body.classList.add('at-frozen');
    // 把脱离文档流的body拉上去！否则页面会回到顶部！
    document.body.style.top = `${-scrollTop}px`;
  } else {
    document.body.style.top = '0px';
    document.body.classList.remove('at-frozen');
    document.documentElement.scrollTop = 0;
  }
}

/**
 *
 * 根据生日计算年龄
 * @param {string} birthday 生日
 * @returns
 */
function getAge(birthday: string) {
  const birthdayArr = birthday.split('-');
  const birthdayYear = parseInt(birthdayArr[0]);
  const birthdayMonth = parseInt(birthdayArr[1]);
  const birthdayDay = parseInt(birthdayArr[2]);
  const today = new Date();
  const nowYear = today.getFullYear();
  const nowMonth = today.getMonth() + 1;
  const nowDay = today.getDate();
  const yearDiff = nowYear - birthdayYear;
  const monthDiff = nowMonth - birthdayMonth;
  const dayDiff = nowDay - birthdayDay;
  if (yearDiff <= 0) return 0;
  if (monthDiff === 0) {
    return dayDiff < 0 ? yearDiff - 1 : yearDiff;
  }
  return monthDiff < 0 ? yearDiff - 1 : yearDiff;
}

/**
 * 获取公司的显示名
 * @param {object} position 职位
 * @return {string} 公司显示名
 */
const getClientDisplayName = (position: Partial<PositionTypes>): string => {
  return position?.clientDisplayName || position?.client?.shortName || position?.client?.name || '';
};

/**
 * 单个城市获取城市name
 * @param {number[]} ids 城市id
 * @param {any[]} cityMap 城市数据
 * @return {string} 城市name
 */
const getCityNameByIds = (ids: number[], cityMap: any[]): string => {
  return ids?.length ? ids.filter(id => cityMap[id]).map((id: number) => cityMap[id].name).join('/') : '-';
};

/**
 * 获取职位基础信息
 * @param {object} position 职位信息
 * @param {object} dictMap 字典数据
 * @return {array} 职位基础信息
 */
const getPositionBaseInfo = (position: Partial<PositionTypes>, dictMap: any): Array<any> => {
  if (!dictMap || !Object.keys(dictMap).length) return [];
  let baseInfo: any[] = [];
  const {
    city,
    workYears,
    salaryType,
    salaryBegin,
    salaryEnd,
    number,
    degree,
    workType,
  } = position;

  if (salaryType) {
    const salary = getSalaryText({ salaryType, salaryBegin, salaryEnd }, dictMap);
    baseInfo.push({
      label: '薪资',
      value: salary,
    });
  }
  // else{
  //   baseInfo.push({
  //     label: '薪资',
  //     value: '面议'
  //   })
  // }
  const clientName = getClientDisplayName(position);
  if (clientName) {
    baseInfo.push({
      label: '公司',
      value: clientName,
    });
  }

  if (city?.length) {
    const cityValue = dictMap?.common?.city[city[0]]?.name;
    if (cityValue) baseInfo.push({
      label: '地点',
      value: cityValue,
    });
  }

  if (workYears) {
    const workYearsValue = (dictMap?.position?.workYears)[workYears];
    if (workYearsValue) baseInfo.push({
      label: '要求',
      value: workYearsValue,
    });
  } else {
    baseInfo.push({
      label: '要求',
      value: '经验不限',
    });
  }

  if (degree) {
    const degreeValue = dictMap?.common?.degree[degree];
    if (degreeValue) baseInfo.push({
      label: '学历',
      value: degreeValue,
    });
  } else {
    baseInfo.push({
      label: '学历',
      value: '学历不限',
    });
  }

  if (workType) {
    const workTypeValue = (dictMap?.position?.workType)[workType];
    if (workTypeValue) baseInfo.push({
      label: '类型',
      value: workTypeValue,
    });
  }

  if (number) {
    baseInfo.push({
      label: '招聘人数',
      value: `招${number}人`,
    });
  }

  return baseInfo;
};

/**
 * 获取薪资文本
 * @param {object} salary 薪资信息
 * @param {object} dictMap 字典数据
 * @return {string} 薪资文本
 */
const getSalaryText = (salary: any, dictMap: any): string => {
  const { salaryType, salaryBegin, salaryEnd } = salary;
  const salaryTypeMap = dictMap?.position?.salaryType[salaryType];
  // salaryType为6表示面议
  if (salaryType === 6 || !salaryTypeMap || (!salaryBegin && !salaryEnd)) {
    return '面议';
  }
  const value = salaryBegin.toString() + (salaryBegin && salaryEnd ? '-' : '') + salaryEnd.toString();
  const unit = '元/' + salaryTypeMap.substring(0, 1);
  return value + unit;
};

// 对象深度合并
const deepMerge = (obj1: { [key: string]: any }, obj2: { [key: string]: any }) => {
  for (const key in obj2) {
    // 如果target(也就是obj1[key])存在，且是对象的话再去调用deepMerge，否则就是obj1[key]里面没这个对象，需要与obj2[key]合并
    obj1[key] = obj1[key] && obj1[key].toString() === '[object Object]' ? deepMerge(obj1[key], obj2[key]) : obj1[key] = obj2[key];
  }
  return obj1;
};

const merge = (target: any[], defaultList: any) => {
  if (!target) return defaultList;
  const newTarget: any = [];
  target.forEach((item, index) => {
    let newItem: any;
    if (defaultList[index]) {
      newItem = {
        ...item,
        singleColorIcon: defaultList[index]['singleColorIcon'],
        doubleColorIcon: defaultList[index]['doubleColorIcon'],
        multiColorIcon: defaultList[index]['multiColorIcon'],
      };
    } else {
      newItem = { ...item };
    }
    newTarget.push(newItem);
  });
  return newTarget;
};

const getTemplateDefaultConfig = (value: string, config: any) => {
  const { basic, category } = TPLS_MAP[value] || {};
  const categoryList = merge(config?.category?.list, category.list);
  let defaultConfig: any;

  defaultConfig = {
    theme: {
      value: value,
    },
    basic: {
      primaryColor: basic.primaryColor,
    },
    category: {
      mode: category.mode,
      iconType: category.iconType,
      list: categoryList,
    },
  };
  if (value === 'xmly' && !config?.nav) {
    defaultConfig.nav = TPLS_MAP[value].nav;
  }
  return defaultConfig;
};

const mergeTplConfig = (value = 'default', config: any) => {
  const defaultConfig = getTemplateDefaultConfig(value, config);
  return deepMerge(config, defaultConfig);
};

const hexToRgbA = (hex: string, opacity = 1) => {
  let c: any;
  if (/^#([A-Fa-f0-9]{3}){1,2}$/.test(hex)) {
    c = hex.substring(1).split('');
    if (c.length == 3) {
      c = [c[0], c[0], c[1], c[1], c[2], c[2]];
    }
    c = '0x' + c.join('');
    return 'rgba(' + [(c >> 16) & 255, (c >> 8) & 255, c & 255].join(',') + `,${opacity})`;
  }
  throw new Error('Bad Hex');
};

const pageReload = () => {
  // 重新登录
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  currentPage.onLoad();
};

const getResumeRequirements = (resume: Partial<ResumeTypes>, dictMap: any) => {
  const info: any[] = [];
  if (resume.gender) {
    info.push(dictMap?.common?.gender[resume.gender.toString()]);
  }
  if (resume.birth) {
    info.push(getAge(resume.birth) + '岁');
  }
  if (resume.degree) {
    info.push(dictMap?.common?.degree[resume.degree.toString()]);
  }
  if (resume.phone) {
    info.push(resume.phone);
  }
  return info;
};

/**
 * 获取年月的picker range
 * @param
 * @return {array}
 */
const MONTHS = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
const getYearAndMonthRange = (soFar: boolean, timeRange: number[] = []) => {
  const displayYears: string[] = [];
  const displayMonths: string[] = MONTHS;
  const beginYear = timeRange[0] || 0;
  const endYear = timeRange[1] || 20;
  for (let i = beginYear; i < endYear; i++) {
    const year = dayjs().subtract(i, 'years').format('YYYY');
    displayYears.push(year + '年');
  }
  if (soFar) {
    displayYears.unshift('至今');
  }
  return [displayYears, displayMonths];
};

/**
 * 检测当前的小程序
 * 是否是最新版本，是否需要下载、更新
 */
const checkUpdateVersion = () => {
  //判断微信版本是否 兼容小程序更新机制API的使用
  if (wx.canIUse('getUpdateManager')) {
    //创建 UpdateManager 实例
    const updateManager = wx.getUpdateManager();
    //检测版本更新
    updateManager.onCheckForUpdate((res: any) => {
      // 请求完新版本信息的回调
      if (res.hasUpdate) {
        //监听小程序有版本更新事件
        updateManager.onUpdateReady(() => {
          wx.showModal({
            title: '更新提示',
            content: '新版本已经准备好，是否重启应用？',
            success: function (res2: any) {
              if (res2.confirm) {
                // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
                updateManager.applyUpdate();
              }
            },
          });
        });
        updateManager.onUpdateFailed(() => {
          // 新版本下载失败
          wx.showModal({
            title: '已经有新版本喽~',
            content: '请您删除当前小程序，到微信 “发现-小程序” 页，重新搜索打开哦~',
          });
        });
      }
    });
  } else {
    //TODO 此时微信版本太低（一般而言版本都是支持的）
    wx.showModal({
      title: '溫馨提示',
      content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。',
    });
  }
};

/**
 * 判断文件后缀
 * @param {any} url 文件路径
 * @return {string} 文件后缀
 */
const getFileSuffix = (url: any): string => {
  let suffix = '';
  if (typeof url === 'string' && url.includes('.')) {
    const words = url.split('.');
    suffix = words[words.length - 1].split('?')[0];
  }
  return suffix;
};

/**
 * 获取小程序素材跳转链接
 * @param {string} link 配置的链接网址
 * @return {string} 跳转链接网址
 */
const MaterialBaseUrlMap = {
  'position': {
    baseUrl: '/packageA/pages/position/list',
  },
  'news': {
    baseUrl: '/packageA/pages/news/detail',
  },
  'thirdLink': {
    baseUrl: '/packageA/pages/webview/index',
  },
  'mp': {
    baseUrl: '/packageA/pages/webview/mp',
  },
  'jinshujuUrl': {
    baseUrl: '/jinshujuUrl',
  },
  'miniappUrl': {
    baseUrl: '/miniappUrl',
  },
  'otherMiniappUrl': {
    baseUrl: '/otherMiniappUrl',
  },
};
const getMaterialUrl = (link: string, pageTitle?: string) => {
  if (!link) return '';
  let jumpLink = '';
  const linkArr = link.split('?');
  const baseUrl = linkArr[0].replace(/^\//, '');
  const searchStr = linkArr[1];
  if (baseUrl && MaterialBaseUrlMap[baseUrl]) {
    jumpLink += MaterialBaseUrlMap[baseUrl]['baseUrl'];
  }
  if (searchStr) {
    if (baseUrl === 'position') {
      jumpLink = jumpLink + `?pageTitle=${pageTitle || '职位列表'}&search=${encodeURIComponent(searchStr)}`;
    } else {
      jumpLink = jumpLink + `?${searchStr}`;
    }
  }
  return jumpLink;
};

/**
 * 解密数据
 * @param {Record<string, any>} data 待解密数据，包含encryptData字段
 * @param auth
 */
const decryptSensitiveData = (data: Record<string, any>, auth: any) => {
  const encryptData = data.encryptData;
  if (!encryptData) return data;

  const key = CryptoJS.enc.Utf8.parse(CryptoJS.MD5(auth.sign).toString(CryptoJS.enc.Hex).substring(0, 16));
  const iv = CryptoJS.enc.Utf8.parse(CryptoJS.MD5(auth.tenant.sign).toString(CryptoJS.enc.Hex).substring(0, 16));
  // 解码密文
  const decodedData = decodeURIComponent(encryptData + '=');
  const decryptedText = CryptoJS.AES.decrypt(decodedData, key, { iv }).toString(CryptoJS.enc.Utf8);
  const decryptData = JSON.parse(decryptedText);
  return { ...data, ...decryptData };
};

export {
  MONTHS,
  handleTouchScroll,
  getAge,
  getClientDisplayName,
  getCityNameByIds,
  getPositionBaseInfo,
  getSalaryText,
  mergeTplConfig,
  hexToRgbA,
  pageReload,
  getResumeRequirements,
  getYearAndMonthRange,
  checkUpdateVersion,
  getFileSuffix,
  getMaterialUrl,
  decryptSensitiveData,
};
