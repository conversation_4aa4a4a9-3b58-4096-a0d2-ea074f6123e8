/**
 * Mixes a color with black. It's different from darken()
 *
 * @param {color} $color
 * @param {number (percentage)} $percent [The amount of black to be mixed in]
 * @return {color}
 *
 * @example
 *   .element {
 *     background-color: shade(#ffbb52, 60%);
 *   }
 *
 *   // CSS Output
 *   .element {
 *     background-color: #664a20;
 *   }
 */
@function shade(
  $color,
  $percent
) {
  @return mix(#000, $color, $percent);
}
