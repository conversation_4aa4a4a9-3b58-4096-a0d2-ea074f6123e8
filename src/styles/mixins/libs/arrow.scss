/**
 * @example scss
 *
 * .element {
 *   @include clearfix;
 * }
 *
 * // CSS Output
 * .element::after {
 *   clear: both;
 *   content: '';
 *   display: block;
 * }
 */
 @mixin arrow {
  padding-right: 22px;
  position: relative;
  text-align: right;
  color: rgba(0, 0, 0, 0.5);
  &::after {
    content: " ";
    width: 12px;
    height: 24px;
    -webkit-mask-position: 0 0;
    mask-position: 0 0;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-size: 100%;
    mask-size: 100%;
    background-color: currentColor;
    color: rgba(0, 0, 0, 0.3);
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D…%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E");
    mask-image: url("data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D…%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E");
    position: absolute;
    top: 50%;
    right: 0;
    margin-top: -12px;
  }
}
