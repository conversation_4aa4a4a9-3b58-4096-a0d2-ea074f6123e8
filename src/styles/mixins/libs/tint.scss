/**
 * Mixes a color with white. It's different from lighten()
 *
 * @param {color} $color
 * @param {number (percentage)} $percent [The amout of white to be mixed in]
 * @return {color}
 *
 * @example
 *   .element {
 *     background-color: tint(#6ecaa6 , 40%);
 *   }
 *
 *   // CSS Output
 *   .element {
 *     background-color: #a8dfc9;
 *   }
 */
@function tint(
  $color,
  $percent
) {
  @return mix(#FFF, $color, $percent);
}
