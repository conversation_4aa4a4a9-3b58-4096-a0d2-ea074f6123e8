/**
 * Default variables
 */

 @import '../mixins/libs/tint';

//  灵招变量
$page-bg: #f8f8fa;
$color-link: #2775FF;
$color-brand: #2775FF !default;
$color-text: #262626;
$color-text-secondary: #656565;
$color-bg-grey: #F7F7F7;
$color-border-shallow: #eee;
$primary-color: #FE3925;
// $primary-color: #FC953D;

$background-color: #f8f8f8;

 $hd: 2 !default; // 基本单位

 /* The Color of O2Team Brand */
 $color-brand-light: #2369E5 !default;
 $color-brand-dark: #92B9FF !default;

 /* Color */
 $color-success: #13CE66 !default;
 $color-error: #FF4949 !default;
 $color-warning: #FFC82C !default;
 $color-info: #78A4FA !default;

 /* Color Palette */
 $color-black-0: #000 !default;
 $color-black-1: #333 !default;
 $color-black-2: #7F7F7F !default;
 $color-black-3: #B2B2B2 !default;

 $color-grey-0: #333 !default;
 $color-grey-1: #666 !default;
 $color-grey-2: #999 !default;
 $color-grey-3: #CCC !default;
 $color-grey-4: #E5E5E5 !default;
 $color-grey-5: #F0F0F0 !default;
 $color-grey-6: #F7F7F7 !default;

 $color-white: #FFF !default;

 /* Text Color */
 $color-text-base: #333 !default; // 文字的基本色
 $color-text-base-inverse: #FFF !default; // 反色
//  $color-text-secondary: #36D57D !default; // 辅助色
 $color-text-placeholder: #C9C9C9 !default;
 $color-text-disabled: #CCC !default;
 $color-text-title: #2C405A !default; // 文章标题
 $color-text-paragraph: #3F536E !default; // 文章段落

 /* Link */
 $color-link: #6190E8 !default;
 $color-link-hover: #79A1EB !default;
 $color-link-active: #4F7DE2 !default;
 $color-link-disabled: #BFBFBF !default;

 /* 背景色 */
 $color-bg: #FFF !default;
 $color-bg-base: #FAFBFC !default;
 $color-bg-light: #ECF5FD !default;
 $color-bg-lighter: tint($color-bg-light, 50%) !default;
//  $color-bg-grey: #F7F7F7 !default;

 /* 边框颜色 */
 $color-border-base: #333 !default;
 $color-border-split: tint($color-border-base, 20%) !default; // 分割线
 $color-border-light: tint($color-border-base, 30%) !default;
 $color-border-lighter: tint($color-border-base, 50%) !default;
 $color-border-lightest: tint($color-border-base, 80%) !default;
 $color-border-grey: #CCC !default;
//  $color-border-shallow: #EEE !default;

 /* 图标颜色 */
 $color-icon-base: #CCC !default;

 /* Border Radius */
 $border-radius-sm: 2px * $hd !default;
 $border-radius-md: 4px * $hd !default;
 $border-radius-lg: 6px * $hd !default;

 $border-radius-circle: 50% !default;

 /* 透明度 */
 $opacity-active: 0.6 !default; // Button 等组件点击态额透明度
 $opacity-disabled: 0.4 !default; // Button 等组件禁用态的透明度

 /* Font */
 $font-size-xs: 10px !default; // 非常用字号，用于标签
 $font-size-sm: 12px !default; // 用于辅助信息
 $font-size-base: 14px !default; // 常用字号
 $font-size-lg: 16px !default; // 常规标题
 $font-size-xl: 18px !default; // 大标题
 $font-size-xxl: 20px !default; // 用于大号的数字

 /* Line Height */
 $line-height-base: 1 !default; // 单行
 $line-height-en: 1.3 !default; // 英文多行
 $line-height-zh: 1.5 !default; // 中文多行

 /* 水平间距 */
 $spacing-h-sm: 5px * $hd !default;
 $spacing-h-md: 8px * $hd !default;
 $spacing-h-lg: 12px * $hd !default;
 $spacing-h-xl: 16px * $hd !default;

 /* 垂直间距 */
 $spacing-v-xs: 3px * $hd !default;
 $spacing-v-sm: 6px * $hd !default;
 $spacing-v-md: 9px * $hd !default;
 $spacing-v-lg: 12px * $hd !default;
 $spacing-v-xl: 15px * $hd !default;

 /* 图标尺寸 */
 $icon-size-sm: 18px * $hd !default;
 $icon-size-md: 22px * $hd !default;
 $icon-size-lg: 36px * $hd !default;

 /* z-index */
 $zindex-divider: 100 !default;
 $zindex-steps: 500 !default;
 $zindex-tab: 600 !default;
 $zindex-form: 700 !default;
 $zindex-nav: 800 !default;
 $zindex-search-bar: 800 !default;
 $zindex-indexes: 805 !default;
 $zindex-flot-layout: 810 !default;
 $zindex-drawer: 900 !default;
 $zindex-modal: 1000 !default;
 $zindex-action-sheet: 1010 !default;
 $zindex-picker: 1010 !default;
 $zindex-curtain: 1080 !default;
 $zindex-message: 1090 !default;
 $zindex-toast: 1090 !default;

 /* timing function */
 $timing-func: cubic-bezier(0.36, 0.66, 0.04, 1) !default;

 /**
 * CSS cubic-bezier timing functions
 * http://bourbon.io/docs/#timing-functions
 */
 $ease-in-quad: cubic-bezier(0.550, 0.085, 0.680, 0.530) !default;
 $ease-in-cubic: cubic-bezier(0.550, 0.055, 0.675, 0.190) !default;
 $ease-in-quart: cubic-bezier(0.895, 0.030, 0.685, 0.220) !default;
 $ease-in-quint: cubic-bezier(0.755, 0.050, 0.855, 0.060) !default;
 $ease-in-sine: cubic-bezier(0.470, 0.000, 0.745, 0.715) !default;
 $ease-in-expo: cubic-bezier(0.950, 0.050, 0.795, 0.035) !default;
 $ease-in-circ: cubic-bezier(0.600, 0.040, 0.980, 0.335) !default;
 $ease-in-back: cubic-bezier(0.600, -0.280, 0.735, 0.045) !default;

 $ease-out-quad: cubic-bezier(0.250, 0.460, 0.450, 0.940) !default;
 $ease-out-cubic: cubic-bezier(0.215, 0.610, 0.355, 1.000) !default;
 $ease-out-quart: cubic-bezier(0.165, 0.840, 0.440, 1.000) !default;
 $ease-out-quint: cubic-bezier(0.230, 1.000, 0.320, 1.000) !default;
 $ease-out-sine: cubic-bezier(0.390, 0.575, 0.565, 1.000) !default;
 $ease-out-expo: cubic-bezier(0.190, 1.000, 0.220, 1.000) !default;
 $ease-out-circ: cubic-bezier(0.075, 0.820, 0.165, 1.000) !default;
 $ease-out-back: cubic-bezier(0.175, 0.885, 0.320, 1.275) !default;

 $ease-in-out-quad: cubic-bezier(0.455, 0.030, 0.515, 0.955) !default;
 $ease-in-out-cubic: cubic-bezier(0.645, 0.045, 0.355, 1.000) !default;
 $ease-in-out-quart: cubic-bezier(0.770, 0.000, 0.175, 1.000) !default;
 $ease-in-out-quint: cubic-bezier(0.860, 0.000, 0.070, 1.000) !default;
 $ease-in-out-sine: cubic-bezier(0.445, 0.050, 0.550, 0.950) !default;
 $ease-in-out-expo: cubic-bezier(1.000, 0.000, 0.000, 1.000) !default;
 $ease-in-out-circ: cubic-bezier(0.785, 0.135, 0.150, 0.860) !default;
 $ease-in-out-back: cubic-bezier(0.680, -0.550, 0.265, 1.550) !default;


