import { useGlobalIconFont } from './components/iconfont/helper';

export default {
  pages: [
    // 'pages/welcome/index',
    // [首页]
    'pages/index/index',
    // 首页 - 搜索框
    'pages/index/search',
    // [报名记录] - index支持未登录访问？
    'pages/flow/index',
    'pages/flow/list',
    // [消息]
    'pages/message/index',
    // [我的]
    'pages/mine/index',
    // 我的 - 个人信息编辑页
    'pages/mine/base',
  ],
  subPackages: [
    {
      root: 'packageA',
      pages: [
        // 首页 - 职位列表页
        'pages/position/list',
        // 首页 - 职位详情页
        'pages/position/detail',
        // 报名结果页
        'pages/result',
        // 职位详情 -> 职位分享页
        'pages/share/position',
        // 职位详情 -> 客户详情页
        'pages/client/detail',
        // 首页 -> 资讯页
        'pages/news/detail',
        // 首页 -> 内嵌webview页
        'pages/webview/index',
        'pages/webview/mp',
        // 首页 -> 金数据表单页
        'pages/jinshuju/index',
        // 我的 - 完善简历页
        'pages/resume/complete',
        // 我的 - 简历预览页
        'pages/resume/index',
        // 我的 - 简历编辑页
        'pages/resume/edit',
        // 我的 - 我的收藏页
        'pages/favs/index',
        // 我的 - 隐私政策页
        'pages/agreement/index',
        // 企业未开通小程序报错
        'pages/error/index',
        // 风格选择页 - 仅demo程序用
        'pages/template/index',
      ],
    },
  ],
  window: {
    backgroundTextStyle: 'dark',
    navigationBarBackgroundColor: '#fff',
    navigationBarTextStyle: 'black',
    // enablePullDownRefresh: true,
  },
  tabBar: {
    custom: false,
    borderStyle: 'black',
    color: '#656565',
    selectedColor: '#333',
    backgroundColor: '#ffffff',
    list: [
      {
        pagePath: 'pages/index/index',
        text: '找工作',
        iconPath: 'images/job.png',
        selectedIconPath: 'images/job-active.png',
      },
      {
        pagePath: 'pages/flow/index',
        text: '报名记录',
        iconPath: 'images/order.png',
        selectedIconPath: 'images/order-active.png',
      },
      {
        pagePath: 'pages/message/index',
        text: '消息',
        iconPath: 'images/message.png',
        selectedIconPath: 'images/message-active.png',
      },
      {
        pagePath: 'pages/mine/index',
        text: '我的',
        iconPath: 'images/mine.png',
        selectedIconPath: 'images/mine-active.png',
      },
    ],
  },
  // eslint-disable-next-line react-hooks/rules-of-hooks
  usingComponents: Object.assign({ 'myradio': 'components/myradio/myradio' }, useGlobalIconFont()),
  requiredPrivateInfos: ['getLocation'],
  permission: {
    'scope.userLocation': {
      desc: '导航',
    },
  },
  plugins: {
    gdPlugin: {
      version: '0.2.4',
      provider: 'wx34b0738d0eef5f78',
    },
  },
};
