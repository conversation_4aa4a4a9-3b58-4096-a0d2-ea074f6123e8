import { Image, View } from '@tarojs/components';
import type { FC } from 'react';
import Taro from '@tarojs/taro';
import './index.scss';
import defaultAvatar from '../../../../images/icon_avatar.svg';

type MineHeaderProps = {
  userInfo: Partial<UserInfoTypes>
  onChange?: (values: Partial<UserInfoTypes>) => void
}
const MineHeader: FC<MineHeaderProps> = ({ userInfo }) => {
  const { name, avatar, phone } = userInfo;

  const getAvatarAndNickName = () => {
    return (
      <View style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }} onClick={() => Taro.navigateTo({ url: `/pages/mine/base?simple=1` })}>
        <Image mode="aspectFill" style={{ border: avatar ? '' : 'solid 1px #e6e6e6' }} src={avatar || defaultAvatar} className="qz-MineHeader-infos__avatar" />
        {name && <View className="qz-MineHeader-infos__nameWrap">
          <View className="qz-MineHeader-infos__name">{name}</View>
        </View>}
      </View>
    );
  };

  const getPhone = () => {
    return phone ? <View className="qz-MineHeader-infos__phoneWrap">
      <View className="qz-MineHeader-infos__phone">{phone}</View>
    </View> : null;
  };

  return (
    <View className="qz-MineHeader">
      <View className="qz-MineHeader-infos">
        {getAvatarAndNickName()}
        {getPhone()}
      </View>
    </View>
  );
};
export default MineHeader;
