import { FC, useEffect, useState } from 'react';
import { container } from '@landrover/business/index';
import Taro, { useDidShow } from '@tarojs/taro';
import { decryptSensitiveData } from '@utils/index';
import { View } from '@tarojs/components';
import QzIcon from '@components/Icon';
import IconFont from '@components/iconfont';
import QzModal from '@components/Modal';
import MineHeader from './components/MineHeader';
import './index.scss';

type MineProps = {};
const Mine: FC<MineProps> = () => {
  const [userInfo, setUserInfo] = useState<Partial<UserInfoTypes>>({
    name: '',
    avatar: '',
    phone: '',
  });
  const { miniProgram } = Taro.getAccountInfoSync();

  const getUserInfo = () => {
    const { auth } = container.userService;
    if (auth) {
      setUserInfo(decryptSensitiveData(auth, auth));
    }
  };

  const [isOpened, setIsOpened] = useState<boolean>(false);
  useEffect(() => {
    setIsOpened(!userInfo?.name && !userInfo?.avatar);
  }, [userInfo]);

  useEffect(() => {
    getUserInfo();
  }, []);

  useDidShow(() => {
    getUserInfo();
  });

  return (
    <View className="container container-mine">
      <MineHeader
        userInfo={userInfo}
        onChange={values => {
          setUserInfo(values);
        }}
      />
      <View className="card-cells">
        <View
          className="card-cell"
          onClick={() => {
            Taro.navigateTo({
              url: `/pages/mine/base`,
            });
          }}
        >
          <View>
            <View className="card-cell__label">
              <QzIcon
                className="card-cell__label-icon"
                type="release"
                size="18"
              />
              基本信息
            </View>
            <QzIcon className="card-cell__icon" type="icon_arrow" size="18" />
          </View>
        </View>

        <View
          className="card-cell"
          onClick={() => {
            Taro.navigateTo({
              url: `/packageA/pages/resume/index`,
            });
          }}
        >
          <View>
            <View className="card-cell__label">
              <QzIcon
                className="card-cell__label-icon"
                type="resume"
                size="18"
              />
              我的简历
              {/* <View className="card-cell__label-tag"></View> */}
            </View>
            <QzIcon className="card-cell__icon" type="icon_arrow" size="18" />
          </View>
        </View>
        <View
          className="card-cell"
          onClick={() => {
            Taro.navigateTo({
              url: `/packageA/pages/favs/index`,
            });
          }}
        >
          <View>
            <View className="card-cell__label">
              <View className="card-cell__label-icon">
                <IconFont name="xihuan3" color="#333333" size={17} />
              </View>
              我的收藏
            </View>
            <QzIcon className="card-cell__icon" type="icon_arrow" size="18" />
          </View>
        </View>

        <View
          onClick={() => {
            Taro.navigateTo({
              url: `/packageA/pages/agreement/index`,
            });
          }}
          className="card-cell"
        >
          <View className="card-cell__label">
            <QzIcon
              className="card-cell__label-icon"
              type="privacy"
              size="20"
            />
            隐私政策
          </View>
          <QzIcon className="card-cell__icon" type="icon_arrow" size="18" />
        </View>
        <View className="card-cell">
          <View className="card-cell__label">
            <QzIcon
              className="card-cell__label-icon"
              type="tag"
              size="18"
            />
            当前版本：{miniProgram.version || miniProgram.envVersion || 'unknown'}
          </View>
        </View>
      </View>

      <QzModal
        customContainer
        container-class="mine-modal"
        isOpened={isOpened}
        title="请授权头像、昵称"
        cancelText="取消"
        confirmText="确定"
        onConfirm={() => {
          Taro.navigateTo({
            url: `/pages/mine/base`,
          });
          setIsOpened(false);
        }}
        onCancel={() => {
          setIsOpened(false);
        }}
      >
        <View style={{
          textAlign: 'center',
          padding: '20px 0 0 0',
          color: '#999',
        }}
        >用于生成您的账号信息</View>
      </QzModal>
    </View>
  );
};
export default Mine;
