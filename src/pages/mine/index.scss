@import '@styles/variables/default.scss';
@import '@styles/mixins/index.scss';
.container-mine{
  height: 100vh;
  background-color: $page-bg;
  .qz-modal__container {
    width: 280px
  }
  .qz-modal__footer .qz-modal__action{
    width: 220px;
  }
}
.card-cells {
  width: 345px;
  margin: 15px auto;
  box-shadow: 0 2.8px 2.2px rgba(0, 0, 0, 0.034);
  background-color: #fff;
  border-radius: 6px;
}
.card-cell {
  padding: 0 15px;
  height: 60px;
  @include display-flex();
  @include align-items(center);
  @include hairline-bottom-relative($color-border-shallow, solid, 1px, 45px, 0);

  &__label {
    font-size: 16px;
    color: #2B2B2B;
    position: relative;
    padding-left: 30px;
    &-icon {
      @include absolute-center(absolute, vertical);
      left: 0;
    }
    &-tag {
      position: absolute;
      top: 2px;
      right: -10px;
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }
  &__icon {
    @include absolute-center(absolute, vertical);
    right: 15px;
  }
  &:last-child::after {
    display: none;
  }
}
