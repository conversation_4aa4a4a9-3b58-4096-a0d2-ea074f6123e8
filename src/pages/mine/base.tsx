import { FC, useState } from 'react';
import { View, Input, Block, Button, Image, Form } from '@tarojs/components';
import { container } from '@landrover/business/index';
import { decryptSensitiveData } from '@utils/index';
import Taro from '@tarojs/taro';
import QzIcon from '@components/Icon';
import List from '@components/List';
import ListItem from '@components/List/components/ListItem';
import NativePicker from '@components/NativePicker';
import { useSelector } from 'react-redux';
import QzButton from '@components/Button';
import { getPhoneNumber } from '@utils/login';
import defaultAvatar from '../../images/icon_avatar.svg';
import './base.scss';

type MineBaseProps = {};
const MineBase: FC<MineBaseProps> = () => {
  const defaultUserInfo = container.userService.auth;
  const [userInfo, setUserInfo] = useState(decryptSensitiveData(defaultUserInfo, defaultUserInfo));
  const { avatar, name, gender, phone } = userInfo;

  const { dictArr = {} } = useSelector((state: any) => state.dict);
  const genderOptions = dictArr?.common?.gender;

  Taro.setNavigationBarColor({
    frontColor: '#000000',
    backgroundColor: '#f8f8fa',
  });

  return (
    <Form
      onSubmit={values => {
        const { value } = values.detail;
        container.userService
        .updateUserProfile({
          ...value,
          avatar,
          gender,
        })
        .then(() => {
          Taro.navigateBack();
        });
      }}
    >
      <View className="container container-mine-base">
        <List>
          <ListItem label="头像">
            <Button
              style={{
                backgroundColor: '#f5f5f5',
                padding: 0,
                margin: 0,
                borderRadius: '36px',
                borderWidth: 'solid 1px red',
                width: '36px',
                height: '36px',
                border: 'none',
              }}
              openType="chooseAvatar"
              onChooseAvatar={e => {
                const { avatarUrl } = e.detail;
                container.saasService
                .fileUpload('file', avatarUrl, {
                  category: 'avatar',
                })
                .then((res: any) => {
                  setUserInfo({ ...userInfo, avatar: res.url });
                });
              }}
            >
              <Image
                mode="aspectFill"
                src={avatar || defaultAvatar}
                style={{
                  width: '36px',
                  height: '36px',
                  borderRadius: '36px',
                }}
              />
            </Button>
          </ListItem>
          <ListItem label="昵称">
            <Input
              type="nickname"
              name="name"
              className="qz-FormItem__value"
              value={name}
              onInput={(e): void => {
                setUserInfo({ ...userInfo, name: e.detail.value });
              }}
              placeholder="请输入昵称"
              placeholderClass="qz-ListItem-placeholder"
            />
          </ListItem>
          <ListItem label="手机">
            {phone ? (
              <Input
                name="phone"
                className="qz-FormItem__value"
                // disabled
                value={phone}
                onInput={(e): void => {
                  setUserInfo({ ...userInfo, phone: e.detail.value });
                }}
                placeholderClass="qz-ListItem-placeholder"
              />
            ) : (
              <QzButton
                customStyle={{
                  margin: 'inherit',
                  border: 'solid 1px #eee',
                  backgroundColor: '#eee',
                  fontSize: '14px',
                }}
                openType="getPhoneNumber"
                onGetPhoneNumber={(e: any) => {
                  getPhoneNumber(e, (res: any) => {
                    setUserInfo({ ...userInfo, phone: res.phone });
                  });
                }}
                size="small"
                circle
                fluid
                type="gray"
              >
                自动获取
              </QzButton>
            )}
          </ListItem>
          <ListItem label="性别">
            <Block>
              <NativePicker
                range={genderOptions}
                value={gender}
                name="gender"
                mode="selector"
                onChange={(val: any) => {
                  setUserInfo({ ...userInfo, gender: val.id });
                }}
                placeholder="请选择性别"
              />
              <QzIcon type="icon_arrow" size="18" />
            </Block>
          </ListItem>
        </List>
        <View className="container-mine-base__btn">
          <QzButton circle full formType="submit" size="large" type="primary">
            保存
          </QzButton>
        </View>
      </View>
    </Form>
  );
};
export default MineBase;
