import Search from '@components/Search';
import { useState, useEffect, useCallback } from 'react';
import { Input, View } from '@tarojs/components';
import { container } from '@landrover/business/index';
import type { FC } from 'react';
import { getCurrentInstance, useReachBottom } from '@tarojs/taro';
import PositionList from '@components/qz/PositionList';
import './search.scss';

type PositionSearchProps = {}
const PositionSearch: FC<PositionSearchProps> = () => {
  const [scrollComplete, setScrollComplete] = useState<boolean>(false);
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [totalNumber, setTotalNumber] = useState<number>(0);
  const [listData, setListData] = useState<PositionTypes[]>([]);
  const [keyword, setKeyword] = useState<string>();
  const { router } = getCurrentInstance();

  const getPositionList = useCallback((params?: any) => {
    container.saasService.getPositions({ keyword, pageSize: 10, ...params }).then((result: { list: any[]; current: number; total: number }) => {
      const { current, list, total } = result;
      setPageIndex(current);
      setListData((prevData: any) => current === 1 ? list : prevData.concat(list));
      setTotalNumber(total);
    });
  }, [keyword]);

  useEffect(() => {
    if (router?.params?.keyword) {
      setKeyword(decodeURIComponent(router.params.keyword));
    }
  }, [router?.params?.keyword]);

  useEffect(() => {
    if (keyword) {
      getPositionList({ current: 1 });
    } else {
      setListData([]);
    }
  }, [getPositionList, keyword]);


  useReachBottom(() => {
    const getLastPageIndex = Math.ceil(totalNumber / 10);

    // 判断是否有下一页数据
    const hasNext = pageIndex < getLastPageIndex;
    // 没有下一页，无数加载更多数据
    if (!hasNext) {
      setScrollComplete(true);
      return;
    }

    const getPageIndex = pageIndex + 1;
    getPositionList({
      current: getPageIndex,
    });
  });

  const handleUpdateListData = (id: number) => {
    const data = [...listData];
    data.forEach(item => {
      if (item.id === id) {
        item.flow = {
          id,
        };
      }
    });
    setListData(data);
  };

  return (
    <View className="container containenr-position-search">
      <Search>
        <Input
          type="text"
          // focus={true}
          className="search-content__input"
          placeholderClass="search-content__input-placeholder"
          placeholder="输入职位名称搜索"
          value={keyword}
          confirmType="search"
          onConfirm={(e: any): void => {
            setKeyword(e.target.value || '');
          }}
        />
      </Search>
      <div className="containenr-position-search__list">
        <PositionList data={listData} onChangeListData={handleUpdateListData} scrollComplete={scrollComplete} />
      </div>
    </View>
  );
};
export default PositionSearch;
