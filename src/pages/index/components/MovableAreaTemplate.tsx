import { MovableArea, MovableView, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { hexToRgbA } from '@utils/index';
import type { FC } from 'react'

type MovableAreaTemplateProps = {
  config:any
}
const MovableAreaTemplate: FC<MovableAreaTemplateProps> = ({config}) => {
  const { value } = config?.theme;
    const primaryColor = config?.basic?.primaryColor;
  return (
    <MovableArea className="movable-area">
                <MovableView
                  className="movable-area__bg"
                  style={{
                    boxShadow:
                      "0px 0px 44px -16px " + hexToRgbA(primaryColor, 1)
                  }}
                  y={252}
                  x={54}
                  onClick={() => {
                    Taro.navigateTo({
                      url: "/packageA/pages/template/index"
                    });
                  }}
                  outOfBounds
                  direction="all"
                  damping={10}
                  friction={1}
                  inertia
                >
                  <View
                    className="movable-area__bg-issue"
                    style={{
                      background: primaryColor || "ff5e56",
                      color: value === "mt" ? "#000" : "#fff"
                    }}
                  >
                    风格
                  </View>
                </MovableView>
              </MovableArea>
  )
}
export default MovableAreaTemplate