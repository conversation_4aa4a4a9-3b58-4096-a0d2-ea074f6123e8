.movable-area {
  position: fixed;
  z-index: 99;
  bottom: 55px;
  right: 15px;
  width: 60px;
  height: 120px;
  &__bg {
    position: absolute;
    z-index: 999;
    right: 20px;
    bottom: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    align-content: center;
    width: 60px;
    height: 60px;
    
    border-radius: 100%;
    background-color: #fff;
    overflow: hidden;
    &-issue {
      width: 50px;
      height: 50px;
      border-radius: 100%;
      background: -webkit-linear-gradient(145deg, #ff5e56 46.66%, #ff8939);
      background: linear-gradient(145deg, #ff5e56 46.66%, #ff8939);
      text-align: center;
      line-height: 50px;
      font-size: 16px;
      color: #fff;
    }
  }
}
