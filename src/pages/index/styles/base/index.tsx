import { FC, useEffect, useState, useCallback } from 'react';
import { container, ui } from '@landrover/business/index';
import Taro, { useReady, useTabItemTap } from '@tarojs/taro';
import { View, Block, Image } from '@tarojs/components';
import Category from '@components/qz/Category';
import IndexNav from '@components/qz/IndexNav';
import SwiperSearch from '@components/qz/SwiperSearch';
import IndexLogo from '@components/qz/IndexLogo';
import IndexTitle from '@components/qz/IndexTitle';
import Banner from '@components/qz/Banner';
import Notice from '@components/qz/Notice';
import ScrollViewWrap from '@components/qz/ScrollViewWrap';
import PositionList from '@components/qz/PositionList';
import PositionFilter from '@components/qz/PositionFilter';
import './index.scss';

type IndexDefaultProps = {
  theme: string;
  filterValues: any;
  onChangeFilterVisible?: (value: boolean) => void;
  onChangeFilterValues?: (values: any) => void;
};

const IndexDefault: FC<IndexDefaultProps> = ({ theme, filterValues, onChangeFilterVisible, onChangeFilterValues }) => {
  const { NAV_BAR_HEIGHT, STATUS_BAR_HEIGHT, NAV_GAP, MENU_BUTTON: { left } } = Taro.getApp().$app;
  const { TPL_CONFIG: { basic: { primaryColor } } } = Taro.getApp().$app;
  const navbarHeight = NAV_BAR_HEIGHT + STATUS_BAR_HEIGHT;

  console.log('render');

  const MT_CONFIG = {
    IndexNav: {},
    FixedSwiperSearch: {
      props: {
        style: {
          background: '#fff',
          width: left - 2 * NAV_GAP + 'px',
        },
      },
    },
    SwiperSearch: {
      props: {
        style: {
          border: 'solid 2px #000',
          height: '40px',
        },
      },
    },
    IndexTitle: {
      props: {
        style: {
          color: '#000',
          fontWeight: '500',
        },
      },
    },
    ScrollViewWrap: {
      props: {
        style: {
          backgroundColor: primaryColor,
        },
        refresherBackground: primaryColor,
      },
    },
    Notice: {},
  };
  const BASE_CONFIG = {
    default: {
      IndexNav: {
        props: {
          style: {
            backgroundColor: '#fff',
            border: 'solid 0.5px #eee',
          },
        },
      },
      FixedSwiperSearch: {
        props: {
          style: {
            width: left - 2 * NAV_GAP + 'px',
          },
        },
      },
      SwiperSearch: {
        props: {
          style: {
            height: '40px',
            backgroundColor: '#f5f5f5',
          },
        },
      },
      IndexTitle: {
        props: {
          style: {
            color: '#000',
          },
        },
      },
      ScrollViewWrap: {},
      Notice: {
        props: {
          style: {
            borderRadius: 0,
          },
        },
      },
    },
    mt: MT_CONFIG,
    verdant: MT_CONFIG,
    classic: {
      IndexNav: {},
      FixedSwiperSearch: {
        props: {
          style: {
            background: '#fff',
            width: left - 2 * NAV_GAP + 'px',
          },
        },
      },
      SwiperSearch: {},
      IndexTitle: {},
      ScrollViewWrap: {
        props: {
          // style: {
          //   backgroundColor:primaryColor
          // },
          refresherBackground: primaryColor,
        },
      },
      Notice: {},
    },
  };
  const indexNavProps = BASE_CONFIG[theme].IndexNav.props || {};
  const fixedSwiperSearchProps =
    BASE_CONFIG[theme].FixedSwiperSearch.props || {};
  const swiperSearchProps = BASE_CONFIG[theme].SwiperSearch.props || {};
  const indexTitleProps = BASE_CONFIG[theme].IndexTitle.props || {};
  const scrollViewWrapProps = BASE_CONFIG[theme].ScrollViewWrap.props || {};
  const noticeProps = BASE_CONFIG[theme].Notice.props || {};
  const [scrollTop, setScrollTop] = useState<number>(0);
  const [positionFilterTop, setPositionFilterTop] = useState<number>(0);
  const [scrollComplete, setScrollComplete] = useState<boolean>(false);
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [totalNumber, setTotalNumber] = useState<number>(0);
  const [listData, setListData] = useState<PositionTypes[]>([]);
  const [searchFixed, setSearchFixed] = useState<boolean>(false);
  const [positionFilterFixed, setPositionFilterFixed] = useState<boolean>(false);

  useEffect(() => {
    if (listData?.length && !positionFilterTop) {
      Taro.nextTick(() => {
        const query = Taro.createSelectorQuery();
        query.select(`#position-filter`).boundingClientRect();
        query.exec(function (res) {
          setPositionFilterTop(res[0].top);
        });
      });
    }
  }, [listData, positionFilterTop]);


  const [refresherTriggered, setRefresherTriggered] = useState<boolean>(false);
  const onRefresherRefresh = () => {
    setRefresherTriggered(true);
    getPositionList({ current: 1 }, refreshCallback);
  };
  const refreshCallback = () => {
    setTimeout(() => {
      ui.showToast('职位已更新', 'none', 1000).then();
      setRefresherTriggered(false);
    }, 500);
  };

  const getPositionList = useCallback((params, callback?: any) => {
      container.saasService.getPositions({ pageSize: 10, ...filterValues, ...params }).then((result: { list: any[]; current: number; total: number }) => {
        const { current, list, total } = result;
        setPageIndex(current);
        setListData((prevData: any) => current === 1 ? list : prevData.concat(list));
        setTotalNumber(total);
        callback && callback();
      });
    },
    [filterValues],
  );

  useEffect(() => {
    console.log('filterValues', filterValues);
    getPositionList({ current: 1 });
  }, [filterValues, getPositionList]);

  useReady(() => {
    setScrollComplete(false);
    getPositionList({ current: 1 });
  });

  useEffect(() => {
    setIsBackTop(false);
    Taro.setTabBarItem({
      index: 0,
      text: scrollTop > 100 ? '回顶部' : '找工作',
      selectedIconPath: scrollTop > 100 ? 'images/back.png' : 'images/job-active.png',
    });
  }, [scrollTop]);

  const [isBackTop, setIsBackTop] = useState(false);
  useTabItemTap(() => setIsBackTop(scrollTop > 100));

  const onScrollToLower = () => {
    const getLastPageIndex = Math.ceil(totalNumber / 10);

    // 判断是否有下一页数据
    const hasNext = pageIndex < getLastPageIndex;
    // 没有下一页，无数加载更多数据
    if (!hasNext) {
      setScrollComplete(true);
      return;
    }

    const getPageIndex = pageIndex + 1;
    getPositionList({
      current: getPageIndex,
    });
  };

  const onScroll = (e: any) => {
    const currentScrollTop = e.detail.scrollTop;
    setScrollTop(currentScrollTop);
    setSearchFixed(currentScrollTop > 44);
    setPositionFilterFixed(currentScrollTop > positionFilterTop - navbarHeight);
  };

  const handleUpdateListData = (id: number) => {
    const data = [...listData];
    data.forEach(item => {
      if (item.id === id) {
        item.flow = { id };
      }
    });
    setListData(data);
  };

  const PositionFilterComp = (
    <PositionFilter
      filterValues={filterValues}
      onChangeFilterValues={values => {
        onChangeFilterValues && onChangeFilterValues(values);
      }}
      onChangeFilterVisible={value => {
        onChangeFilterVisible && onChangeFilterVisible(value);
      }}
    />
  );

  return (
    <View className={`container container-theme-${theme}`} style={{ paddingTop: navbarHeight }}>
      {/* hack:预加载图片，以使动态设置的tabbar图标生效 */}
      <View style={{ display: 'none' }}>
        <Image src={require('@images/back.png')} />
      </View>
      {/* 导航模块 */}
      <IndexNav searchFixed={searchFixed} {...indexNavProps}>
        {searchFixed ? (
          <SwiperSearch {...fixedSwiperSearchProps} />
        ) : (
          <Block>
            <IndexLogo />
            <IndexTitle {...indexTitleProps} />
          </Block>
        )}
      </IndexNav>
      <View>
        {positionFilterFixed ? (
          <View
            className="position-filter__fixedWrap"
            style={{
              position: 'fixed',
              top: navbarHeight,
              left: 0,
              width: '100%',
              zIndex: 9,
            }}
          >
            {PositionFilterComp}
          </View>
        ) : null}
      </View>

      <View className="inner">
        <ScrollViewWrap
          onChangeRefresherRefresh={onRefresherRefresh}
          refresherTriggered={refresherTriggered}
          onChangeScrollToLower={onScrollToLower}
          onScroll={onScroll}
          isBackTop={isBackTop}
          scrollComplete={scrollComplete}
          {...scrollViewWrapProps}
        >
          <View className="content">
            {theme === 'default' ? (
              <View className="content-hd">
                <View className="content-hd__inner">
                  <SwiperSearch {...swiperSearchProps} />
                  <Banner />
                </View>
                <Category style={{ padding: 0 }} />
              </View>
            ) : (
              <Block>
                <SwiperSearch {...swiperSearchProps} />
                <Banner />
                <Category />
              </Block>
            )}
            <Notice {...noticeProps} />
            <View className="content__positionListWrap">
              <View id="position-filter">{PositionFilterComp}</View>
              <PositionList
                data={listData}
                onChangeListData={handleUpdateListData}
                scrollComplete={scrollComplete}
              />
            </View>
          </View>
        </ScrollViewWrap>
      </View>
    </View>
  );
};
export default IndexDefault;
