@import '@styles/variables/default.scss';

.container-theme-default{
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
    .qz-category{
      padding:0;
    }
    .inner{
        height: 0;
        /* 充满剩余空间 */
        flex-grow: 1;
        position: relative;
    }
    .content{
        display: flex;
        flex-direction: column;
        gap: 10px;
        &__positionListWrap{
            background-color: #fff;
        }
        &-hd{
          display: flex;
          flex-direction: column;
          gap: 10px;
          background-color: #fff;
          padding: 10px 0;
          &__inner{
            display: flex;
            flex-direction: column;
            gap: 10px;
            padding: 0 15px;
          }
        }
    }
}

.container-theme-classic{
  height: 100vh;
  display: flex;
  flex-direction: column;
  .inner{
      height: 0;
      /* 充满剩余空间 */
      flex-grow: 1;
      position: relative;
  }
  .content{
    // border-top-left-radius: 24px;
    // border-top-right-radius: 24px;
    padding: 15px 15px 0 15px;
    background-color: #F5F5F5;
    display: flex;
    flex-direction: column;
    gap: 15px;
    &__positionListWrap{
        background-color: #fff;
        border-radius: 10px;
        overflow: hidden;
    }
  }
  .search-container{
      padding: 15px;
  }
  .position-filter__fixedWrap{
    .qz-PositionFilter{
      background-color: #f5f5f5;
    }
  }
}

.container-theme-mt,
.container-theme-verdant{
  height: 100vh;
  display: flex;
  flex-direction: column;
  .inner{
      height: 0;
      /* 充满剩余空间 */
      flex-grow: 1;
      position: relative;
  }
  .content{
      border-top-left-radius: 24px;
      border-top-right-radius: 24px;
      padding: 15px 15px 0 15px;
      background-color: #F5F5F5;
      display: flex;
      flex-direction: column;
      gap: 15px;
      &__positionListWrap{
        background-color: #fff;
        border-radius: 10px;
        overflow: hidden;
    }
  }
  .search-container{
      padding: 15px;
  }
  .position-filter__fixedWrap{
    .qz-PositionFilter{
      background-color: #f5f5f5;
    }
  }
  
}

