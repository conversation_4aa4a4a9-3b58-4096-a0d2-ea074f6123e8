@import '@styles/variables/default.scss';

.container-indexXmly{
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color:#f5f5f5;
    .page-container{
        /* background: rebeccapurple; */
        height: 0;
        /* 充满剩余空间 */
        flex-grow: 1;
    }
    .page-swiper{
        height: 100%;
        width:100%;
        &__item{
            &-container{
                padding: 15px;
            }
        }
        
    }
}