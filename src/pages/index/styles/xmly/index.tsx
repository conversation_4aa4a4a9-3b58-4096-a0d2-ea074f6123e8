import ScrollNav from "@components/qz/ScrollNav";
import ScrollViewIndex from "@components/qz/ScrollViewIndex";
import TopSearch from "@components/qz/TopSearch";
import { Swiper, SwiperItem, View } from "@tarojs/components";
import Taro, { useTabItemTap } from "@tarojs/taro";
import { FC, useEffect, useState } from "react";
import NavContent from "@components/qz/NavContent";
import "./index.scss";

type IndexXmlyProps = {
  filterValues: any,
  onChangeFilterVisible?:(value:boolean)=>void
};
const IndexXmly: FC<IndexXmlyProps> = ({filterValues,onChangeFilterVisible}) => {
  const {
    MENU_BUTTON,
    TPL_CONFIG,
  } = Taro.getApp().$app;
  const { list } = TPL_CONFIG.nav || {};
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [scrollTop, setScrollTop] = useState<number>(0);
  const [mode, setMode] = useState<string>("dark");

  useEffect(() => {
    if (currentIndex > 0) {
      setMode("light");
    } else {
      setMode(scrollTop > 160 ? "light" : "dark");
    }
  }, [currentIndex]);
  
  useEffect(()=>{
    setIsBackTop(false);
    Taro.setTabBarItem({
      index: 0,
      text: scrollTop>100 ? '回顶部':'找工作',
      selectedIconPath: scrollTop>100 ?'images/back.png':'images/job-active.png'
    })
  },[scrollTop])

  const [isBackTop,setIsBackTop] = useState(false);
  useTabItemTap(()=>{
    setIsBackTop(scrollTop>100)
  })
  return (
    <View
      className="container container-indexXmly"
      style={{
        paddingTop: MENU_BUTTON.bottom + 40
      }}
    >
      <TopSearch mode={mode} style={{
        height: MENU_BUTTON.height,
        boxSizing:'content-box'
      }} />
      <ScrollNav
        mode={mode}
        currentIndex={currentIndex}
        onChangePageIndex={(targetIndex: number) => {
          setCurrentIndex(targetIndex);
        }}
      />
      <View className="page-container">
        <Swiper
          className="page-swiper"
          current={currentIndex}
          onChange={e => {
            setCurrentIndex(e.detail.current);
          }}
        >
          {list.map((item, index) => {
            if (index === 0) {
              return (
                <SwiperItem className="page-swiper__item">
                  <ScrollViewIndex
                    filterValues={filterValues} 
                    onChangeFilterVisible={(value)=>{
                      onChangeFilterVisible && onChangeFilterVisible(value);
                    }}
                    isRefresh={index===currentIndex}
                    isBackTop={isBackTop}
                    onChangeScrollTop={(value: number) => {
                      setMode(value > 160 ? "light" : "dark");
                      setScrollTop(value);
                    }}
                  />
                </SwiperItem>
              );
            } else {
              return (
                <SwiperItem key={item.name} className="page-swiper__item">
                  <NavContent isRefresh={index===currentIndex} navData={item} />
                </SwiperItem>
              );
            }
          })}
        </Swiper>
      </View>
    </View>
  );
};
export default IndexXmly;
