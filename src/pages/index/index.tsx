import { FC, useCallback, useState } from 'react';
import Taro, { useShareAppMessage } from '@tarojs/taro';
import imageUrl from '@images/share.jpg';
import { View } from '@tarojs/components';
import PositionFilterPageContainer from '@components/qz/PositionFilterPageContainer';
import ConfigLoading from '@components/qz/ConfigLoading';
import SharePageContainer from '@components/SharePageContainer';
import { useDispatch, useSelector } from 'react-redux';
import { setShareData } from '@stores/actions/share';
import { stringify } from 'querystring';
import MovableAreaTemplate from './components/MovableAreaTemplate';
import IndexBase from './styles/base';
import IndexXmly from './styles/xmly';
import './index.scss';

type ComponentNameProps = {};
const INDEX_COMP_MAP = {
  xmly: IndexXmly,
  default: IndexBase,
  mt: IndexBase,
  verdant: IndexBase,
  classic: IndexBase,
};
const ComponentName: FC<ComponentNameProps> = () => {
  const { extConfig, ACCOUNT_INFO, TPL_CONFIG } = Taro.getApp().$app;
  const [shareImageUrl, setShareImageUrl] = useState<string>('');
  const dispatch = useDispatch();
  const shareData = useSelector((state: any) => state.share);
  const positionData = shareData?.position;
  const { appId } = ACCOUNT_INFO.miniProgram;
  const isDemo = appId === 'wx61ce436f0244a37a';

  const [config, setConfig] = useState(TPL_CONFIG);
  const [filterVisible, setFilterVisible] = useState(false);
  const [filterValues, setFilterValues] = useState<any>({});

  const onChangeFilterValues = useCallback((values: any) => setFilterValues(values), []);
  const onChangeFilterVisible = useCallback((visible: boolean) => setFilterVisible(visible), []);

  useShareAppMessage(() => {
    if (shareData?.visible) {
      const shareParams = {
        id: positionData?.id,
        invitor: Taro.getStorageSync('invitor') || undefined,
      };
      return {
        title: `${positionData?.client?.shortName || positionData?.client?.name}·${positionData?.name}`,
        path: '/packageA/pages/position/detail?' + stringify(shareParams),
        imageUrl: shareImageUrl,
      };

    } else {
      return {
        title: `上${extConfig.nickname}，轻松找到好工作`,
        path: '/pages/index/index',
        imageUrl: imageUrl,
      };
    }
  });

  const getContent = () => {
    const { value } = config?.theme;
    const WrappedComponent = INDEX_COMP_MAP[value];
    console.log('getContent', value);
    return <WrappedComponent
      theme={value}
      onChangeFilterValues={onChangeFilterValues}
      filterValues={filterValues}
      onChangeFilterVisible={onChangeFilterVisible}
    />;
  };

  return (
    <View>
      {!config && <ConfigLoading onInit={(values) => setConfig(values)} />}
      {config && <View>{getContent()}</View>}
      {isDemo && config && <MovableAreaTemplate config={config} />}
      <PositionFilterPageContainer
        config={config}
        values={filterValues}
        onChange={(values) => setFilterValues(values)}
        visible={filterVisible}
        onCancel={() => setFilterVisible(false)}
      />
      <SharePageContainer
        onChangeShareImg={(url) => setShareImageUrl(url)}
        onCancel={() => dispatch(setShareData({ visible: false }))}
        visible={shareData?.visible}
      />
    </View>
  );
};
export default ComponentName;
