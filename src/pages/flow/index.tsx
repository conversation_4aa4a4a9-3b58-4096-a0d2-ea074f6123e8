import { FC, useEffect, useState, useCallback } from 'react';
import { container } from '@landrover/business/index';
import Taro, { useDidShow, useReachBottom } from '@tarojs/taro';
import { View } from '@tarojs/components';
import Loading from '@components/Loading';
import Empty from '@components/Empty';
import NoMore from '@components/NoMore';
import FlowCard from '@components/FlowCard';
import classNames from 'classnames';
import QzButton from '@components/Button';
import { getPhoneNumber } from '@utils/login';
import './index.scss';

type HomeProps = {}
const Home: FC<HomeProps> = () => {
  const { TPL_CONFIG } = Taro.getApp().$app;
  const { radiusStyle } = TPL_CONFIG.basic;
  const [isLogin, setIsLogin] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [scrollComplete, setScrollComplete] = useState<boolean>(false);
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [totalNumber, setTotalNumber] = useState<number>(0);
  const [listData, setListData] = useState<FlowTypes[]>([]);

  Taro.setBackgroundTextStyle({
    textStyle: 'dark',
  });

  useEffect(() => {
    setIsLogin(container.userService.isLogin());
  }, []);

  useDidShow(() => {
    setIsLogin(container.userService.isLogin());
  });

  const getFlowList = useCallback((params: any, callback?: any) => {
    if (!isLogin) return;
    container.saasService.getFlows({ pageSize: 10, ...params }).then((result: { list: any[]; current: number; total: number }) => {
      const { current, list, total } = result;

      setLoading(false);
      setPageIndex(current);
      setListData((prevData: any) => current === 1 ? list : prevData.concat(list));
      setTotalNumber(total);
      callback && callback();
    });
  }, [isLogin]);

  useDidShow(() => {
    getFlowList({
      current: 1,
    });
  });

  useEffect(() => {
    if (isLogin) {
      getFlowList({
        current: 1,
      });
    }
  }, [getFlowList, isLogin]);

  useReachBottom(() => {
    const getLastPageIndex = Math.ceil(totalNumber / 10);
    const hasNext = pageIndex < getLastPageIndex;
    if (!hasNext) {
      setScrollComplete(true);
      return;
    }

    const getPageIndex = pageIndex + 1;
    getFlowList({
      current: getPageIndex,
    });
  });

  const updateFlowData = (id: number) => {
    const data: FlowTypes[] = [];
    listData.forEach((item) => {
      if (item.id !== id) {
        data.push(item);
      }
    });
    setListData(data);
  };

  const getContent = () => {
    if (!isLogin)
      return (
        <View>
          <Empty message="登录后查看报名信息" />
          <QzButton
            type="primary"
            openType="getPhoneNumber"
            onGetPhoneNumber={getPhoneNumber}
            circle
            customStyle={{
              width: '120px',
              marginTop: '16px',
            }}
          >
            立即登录
          </QzButton>
        </View>
      );
    if (loading) {
      return <Loading />;
    }
    if (!loading && !listData.length) return <Empty message="暂无数据" />;
    return (
      <View className={classNames('container flow-container', { 'config-container-padding': radiusStyle === 'round' })}>
        {!!listData.length && (
          <View>
            {listData.map((item) => (
              <FlowCard
                key={item.id}
                flowData={item}
                onChange={(id) => {
                  updateFlowData(id);
                }}
              />
            ))}
            {scrollComplete && <NoMore>没有更多数据了</NoMore>}
          </View>
        )}
      </View>
    );
  };
  return (
    <View>
      {getContent()}
    </View>
  );
};
export default Home;
