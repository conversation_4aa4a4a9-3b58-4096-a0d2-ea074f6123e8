import QzButton from "@components/Button";
import Empty from "@components/Empty";
import { View } from "@tarojs/components";
import { container } from "@landrover/business/index";
import { FC, useEffect, useState } from "react";
import { useDidShow } from "@tarojs/taro";
import { getPhoneNumber } from "@utils/login";

type MessageProps = {};
const Message: FC<MessageProps> = () => {
  const [isLogin, setIsLogin] = useState<boolean>(false);

  useEffect(() => {
    setIsLogin(container.userService.isLogin());
  }, []);

  useDidShow(() => {
    setIsLogin(container.userService.isLogin());
  });

  const getContent = () => {
    if (!isLogin)
      return (
        <View>
          <Empty message="登录后查看消息" />
          <QzButton
            type="primary"
            openType="getPhoneNumber"
            onGetPhoneNumber={getPhoneNumber}
            circle
            customStyle={{
              width: '120px',
              marginTop: '16px',
            }}>
            立即登录
          </QzButton>
        </View>
      );
      

    return <Empty message="暂无消息" />;
  };

  return (
    <>
      {getContent()}
    </>
  );
};
export default Message;
