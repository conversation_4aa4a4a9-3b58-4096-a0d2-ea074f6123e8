import BaseUserService from '../landrover/business/service/user.service'

/**
 * 用户中心服务
 *
 * @export
 * @class UserService
 * @extends {BaseUserService}
 */
export default class UserService extends BaseUserService {
  /**
   * 微信登录相关信息
   *
   * userInfo 后台处理过的微信账号的用户信息
   * sessionId 后台处理过交由客户端保存的会话标识
   *
   * @type {({
   *     userInfo: UserInfoForWeixin | null,
   *     sessionId: string | null
   *   })}
   * @memberof UserService
   */
  weixin: {
    sessionId: string | null
  } = {
      sessionId: null
    }

  /**
   * 微信登录的异步 promise
   *
   * @type {(Promise<{ sessionId: string }> | null)}
   * @memberof UserService
   */
  promiseForWeixinLogin: Promise<{
    sessionId: string
  }> | null = null

  /**
   *
   *
   * 角色名称
   * 'hr' 指HR
   * 'interviewer' 指面试官
   *
   * @type {(RoleEntity | null)}
   * @memberof UserService
   */
  role: string | null = null

  location: Array<{}>

  address: object = {}

  constructor() {
    super()
  }


  /**
   * 是否登录
   *
   * @returns {boolean}
   * @memberof UserService
   */
  isLogin(): boolean {
    return this.isAuthAvailable()
  }

  /**
   * 退出登录接口
   *
   * @returns {Promise<boolean>}
   * @memberof UserService
   */
   userLogout(): Promise<void> {
    return this.request('/logout', 'POST',{
      // weixin: 1
    }).then(res => {
      // 退出登录成功后,清除用户信息
      if (res) {
        this.clearUserInfo()
      }
      return res
    })
  }




  /**
  * 注册
  *
  * @param {string} code wx.login返回的code
  */
  register(params): Promise<void> {
    return this.request(
      '/register',
      'POST',
      params,
      {},
      true, // authForCookie
      true // getCookie
    ).then(result => {
      const expireMillis = this.p_timestampFromNowWithDay(30)
      this.auth = this.userInfo = {
        expireIn: expireMillis,
        ...result
      }
      this.saveUserInfo()
      return result
    }).catch(err => {
      console.error(err)
      return Promise.reject(err)
    })
  }

  /**
  * 微信静默登录
  *
  * @param {object} params
  */
  getWxAuthLogin(params: { code: string }): Promise<void> {
    return this.request(
      '/miniprogram/login',
      'POST',
      params,
      {},
      false, // authForCookie
      true // getCookie
    ).then(result => {
      const expireMillis = this.p_timestampFromNowWithDay(30)
      this.auth = this.userInfo = {
        expireIn: expireMillis,
        ...result
      }
      this.saveUserInfo()
      return result
    }).catch(err => {
      console.error(err)
      return Promise.reject(err)
    })
  }

  /**
  * 根据code获取手机号
  *
  * @param {object} params 
  */
  getPhoneByCode(params: { code: string }): Promise<void> {
    return this.request(
      '/miniprogram/get_phone_by_code',
      'GET',
      params,
      {},
      true, // authForCookie
      false // getCookie
    ).then(result => {
      return result
    }).catch(err => {
      console.error(err)
      return Promise.reject(err)
    })
  }

  /**
  * 获取用户信息
  *
  */
  getUserProfile(): Promise<void> {
    return this.request(
      '/user/profile',
      'GET',
      {},
      {},
      true, // authForCookie
      true // getCookie
    ).then(result => {
      return result
    }).catch(err => {
      return Promise.reject(err)
    })
  }

  /**
   * 更新用户信息
   * @param {object} userInfo
   */
   updateUserProfile(userInfo): Promise<void> {
    return this.request(
      '/user/profile',
      'PUT',
      userInfo,
      {},
      true, // authForCookie
      true // getCookie
    ).then(result => {
      const expireMillis = this.p_timestampFromNowWithDay(30)
      this.auth = this.userInfo = {
        expireIn: expireMillis,
        ...result
      }
      this.saveUserInfo()
      return result
    })
  }

  /**
   * 修改手机号
   * @param {object} params
   */
   updatePhone(params:{
    phone: string
    verifyCode: string
   }): Promise<void> {
    return this.request(
      '/user/update_phone',
      'PUT',
      params,
      {},
      true, // authForCookie
      true // getCookie
    ).then(result => {
      const auth = this.auth;
      this.auth = this.userInfo = {
        ...auth,
        phone: result.phone
      }
      this.saveUserInfo()
      return result
    })
  }

  /**
   * 获取手机验证码
   * @param {number} phone
   */
  sendVcode(phone: number): Promise<void> {
    return this.request(
      '/send_verify_code',
      'POST',
      {
        phone,
        // test: 1
      }
    )
  }

  /**
   * 短信登录
   * @param {object} params
   */
  smsLoginForAccount(params): Promise<object | null | undefined> {
    return this.request(
      '/verify_code_login',
      'POST',
      params,
      {},
      false, // authForCookie
      true // getCookie
    ).then(result => {
      const expireMillis = this.p_timestampFromNowWithDay(30)
      this.auth = this.userInfo = {
        expireIn: expireMillis,
        ...result
      }

      this.saveUserInfo()
      return this.auth
    }).catch(err => {
      console.error(err)
      return Promise.reject(err)
    })
  }

  /**
  * 密码登录
  * @param {object} params
  */
  loginForAccount(params: { phone: string, password: string }): Promise<object | null | undefined> {
    return this.request(
      '/login',
      'POST',
      params,
      {},
      false, // authForCookie
      true // getCookie
    ).then(result => {
      const expireMillis = this.p_timestampFromNowWithDay(30)
      this.auth = this.userInfo = {
        expireIn: expireMillis,
        ...result
      }
      this.saveUserInfo()
      return this.auth
    }).catch(err => {
      console.error(err)
      return Promise.reject(err)
    })
  }
}
