import Taro from '@tarojs/taro';
import Service from '../landrover/business/service/base.service';

export default class SAASService extends Service {
  constructor() {
    // 初始化行为
    super();
    let extConfig = Taro.getExtConfigSync ? Taro.getExtConfigSync() : {};
    this.baseUrl = {
      dev: extConfig.domain + '/api',
      prd: extConfig.domain + '/api',
    };
  }

  // setup(): Promise<void> {
  //   // 启动行为
  //   return super.setup()
  // }

  // getImagePath(): string {
  //   const imagePath = {
  //     dev: 'http://uimg.testing2.ifchange.com',
  //     gqc: 'https://uimg.ifchange.com',
  //     prd: 'https://uimg.ifchange.com'
  //   }
  //   const env = this.getENV()
  //   return imagePath[env]
  // }

  /**
   * 职位列表
   * @param {object} [params] 筛选参数
   * @return {Promise} 职位列表
   */
  getPositions(params?: Record<string, any>): Promise<void> {
    return this.request('/positions', 'GET', params);
  }

  /**
   * 获取字典数据
   * @param {object} [params] 筛选参数
   * @return {Promise} 字典数据
   */
  getDictData(params?: Record<string, any>): Promise<void> {
    return this.request('/common/dict', 'GET', params);
  }

  /**
   * 获取常量数据
   * @param {object} [params] 筛选参数
   * @return {Promise} 常量数据
   */
  getConstantData(params?: Record<string, any>): Promise<void> {
    return this.request('/common/constant', 'GET', params);
  }

  /**
   * 获取用户信息
   * @return {Promise} 用户信息
   */
  getUserInfo(): Promise<void> {
    return this.request('/user/profile', 'GET', {});
  }

  /** 投递记录 GET /api/users/flows */
  // export async function flows(params?:{
  //   current?: number,
  //   pageSize?: number
  // }) {
  //   return request('/api/user/flows', {
  //     params,
  //   });
  // }

  /**
   * 职位投递
   * @param {object} body 筛选参数
   * @return {Promise} 投递结果
   */
  updateFlow(body: { invitor?: string; positionId: number }): Promise<any> {
    const invitor = Taro.getStorageSync('invitor');
    if (invitor) {
      body.invitor = invitor;
    }
    return this.request('/flows', 'POST', body);
  }

  /**
   * 获取简历详情
   * @param {number} [full] 是否获取完整简历数据，0：否，1：是（默认）
   * @return {Promise} 简历详情
   */
  getResumeDetail(full: number = 1): Promise<void> {
    return this.request('/resumes', 'GET', { full });
  }

  /**
   * 更新简历
   * @param {object} body 简历数据
   * @return {Promise} 更新结果
   */
  editBaseResume(body: Record<string, any>): Promise<any> {
    return this.request('/resumes', 'PUT', body);
  }

  /**
   * 更新简历模块（教育经历/工作经历/项目经理）
   * @param {string} sectionName 模块名称
   * @param {object} body 模块数据
   * @return {Promise} 模块更新结果
   */
  updateResumeSection(sectionName: string, body: EducationTypes): Promise<any> {
    return this.request(`/resumes/${sectionName}/${body.id}`, 'PUT', body);
  }

  /**
   * 添加简历模块（教育经历/工作经历/项目经理）
   * @param {string} sectionName 模块名称
   * @param {object} body 模块数据
   * @return {Promise} 模块添加结果
   */
  addResumeSection(sectionName: string, body: EducationTypes): Promise<any> {
    return this.request(`/resumes/${sectionName}`, 'POST', body);
  }

  /**
   * 删除简历模块（教育经历/工作经历/项目经理）
   * @param {string} sectionName 模块名称
   * @param {number} sectionId 模块ID
   * @return {Promise} 模块删除结果
   */
  deleteResumeSection(sectionName: string, sectionId: number): Promise<any> {
    return this.request(`/resumes/${sectionName}/${sectionId}`, 'DELETE');
  }


  /**
   * 获取职位详情
   * @param {number} id 职位ID
   * @return {Promise} 职位详情
   */
  getPositionDetail(id: number): Promise<void> {
    return this.request('/positions/' + id, 'GET', {});
  }

  /**
   * 获取客户详情
   * @param {number} id 客户ID
   * @return {Promise} 客户详情
   */
  getClientDetail(id: number): Promise<void> {
    return this.request('/clients/' + id, 'GET', {});
  }

  /**
   * 投递列表
   * @param {object} [params] 筛选参数
   * @return {Promise} 投递列表
   */
  getFlows(params?: Record<string, any>): Promise<void> {
    return this.request('/flows', 'GET', params);
  }


  /**
   * 取消投递
   * @param {number} id 投递ID
   */
  cancelDeliver(id: number): Promise<any> {
    return this.request('/flows/' + id, 'DELETE');
  }

  /**
   * 职位访问统计
   * @param {number} id 职位ID
   * @param {object} bodyParams 统计参数
   * @return {Promise} 统计结果
   */
  trackPosition(id: number, bodyParams: { shareUser: string; shareScene: number; creatorId?: number; trackId?: number }): Promise<any> {
    return this.request('/positions/' + id + '/track', 'POST', bodyParams);
  }

  /**
   * 生成指定职位的小程序码
   * @param {number} id 职位ID
   * @return {Promise} 生成结果
   */
  getKefuUrl(id: number): Promise<void> {
    return this.request('/positions/' + id + '/kefu', 'GET');
  }

  /**
   * 获取网站配置
   */
  getMpConfig(): Promise<void> {
    return this.request('/miniprogram/config', 'GET');
  }

  /**
   * [设置或获取租户C端是否授权访问模式]
   */
  getWechatAuthMode(params: any): Promise<void> {
    return this.request('/wechat/auth_mode', 'GET', params);
  }

  /**
   * 资讯详情
   * @param {number} id 资讯ID
   * @return {Promise} 资讯详情
   */
  getNewsDetail(id: number): Promise<void> {
    return this.request('/news/' + id, 'GET');
  }

  /**
   * 生成小程序urlScheme
   * @param {object} params 参数
   * @return {Promise} 生成结果
   */
  getSchemeUrl(params: { path: string; query: string; version: 'develop' | 'trial' | 'release' }): Promise<void> {
    return this.request('/miniprogram/scheme', 'GET', params);
  }

  /**
   * 收藏职位
   * @param {number} positionId 职位ID
   * @return {Promise} 收藏结果
   */
  addPositionFavs(positionId: number): Promise<any> {
    return this.request('/positions/favs', 'POST', { positionId });
  }

  /**
   * 取消收藏职位
   * @param {number} positionId 职位ID
   * @return {Promise} 取消结果
   */
  cancelPositionFavs(positionId: number): Promise<any> {
    return this.request('/positions/favs/' + positionId, 'DELETE');
  }

  /**
   * 职位收藏列表
   * @param {object} [params] 筛选参数
   * @return {Promise} 收藏列表
   */
  getFavs(params?: Record<string, any>): Promise<void> {
    return this.request('/positions/favs', 'GET', params);
  }

  /**
   * 职位访问数据
   * @param {number} id 职位ID
   * @return {Promise} 访问数据
   */
  getPositionTrackData(id: number): Promise<void> {
    return this.request(`/positions/${id}/track_data`, 'GET');
  }

  /**
   * 上传文件
   * @param {string} name 字段名
   * @param {string} filePath 文件路径
   * @param {object} [formData] 表单数据
   * @return {Promise} 上传结果
   */
  fileUpload(name: string = 'file', filePath: string, formData?: object): Promise<any> {
    return this.uploadFile('/common/upload', filePath, name, formData);
  }
}
