// @flow
import wxapi from '../../utils/wxapp-promise'

export default class Request implements RequestVirtualClass {
  request<T>(
    url: string,
    method: RequestMethod,
    data: { ['string']: T },
    header: { ['string']: T }
  ): Promise<T> {
    return wxapi.request({
      url: url,
      method: method,
      data: data,
      header: header
    })
  }

  uploadFile<T>(
    url: string,
    filePath: string,
    name: string,
    data: { ['string']: T },
    header: { ['string']: T }
  ): Promise<T> {
    return wxapi.uploadFile({
      url: url,
      filePath: filePath,
      name: name,
      formData: data,
      header: header
    })
  }

  checkSessionForWeixin(): Promise<void> {
    return wxapi.checkSession()
  }

  loginForWeixin(): Promise<{ code: string; errMsg: string }> {
    return wxapi.login()
  }

  getUserInfoForWeixin(withCredentials: boolean): Promise<UserInfoFromMiniProgram> {
    return wxapi.getUserInfo({ withCredentials })
  }

  authorizeForWeixin(scope: 'scope.userInfo' | ''): Promise<{ errMsg: string }> {
    if (wxapi.authorize) {
      return wxapi.authorize({ scope })
    } else {
      if (scope === 'scope.userInfo') {
        return wxapi
          .getUserInfo()
          .then(() => {
            return { errMsg: '使用 getUserInfo() 获取权限成功' }
          })
          .catch(() => {
            return { errMsg: '使用 getUserInfo() 获取权限失败' }
          })
      } else {
        return Promise.reject()
      }
    }
  }

  getSettingForWeixin(): Promise<{ authSetting }> {
    if (wxapi.getSetting) {
      return wxapi.getSetting()
    } else {
      const authSetting = {}
      return Promise.resolve({ authSetting })
    }
  }

  openSettingForWeixin<T>(): Promise<{ authSetting: T }> {
    return wxapi.openSetting()
  }
}
