// @flow
import wxapi from '../../utils/wxapp-promise'

export default class Storage implements StorageVirtualClass {
  setItem(key: string, value: string | number | object): Promise<void> {
    return wxapi.setStorage(key, value)
  }

  setItemSync(key: string, value: string | number | object): boolean {
    try {
      wx.setStorageSync(key, value)
      return true
    } catch (e) {
      console.error(e)
      return false
    }
  }

  getItem<T>(key: string): Promise<T> {
    return wxapi.getStorage(key)
  }

  getItemSync(key: string): any {
    try {
      return wx.getStorageSync(key)
    } catch (e) {
      console.error(e)
      return {}
    }
  }

  removeItem(key: string): Promise<void> {
    return wxapi.removeItem(key)
  }

  removeItemSync(key: string): boolean {
    try {
      wx.removeStorageSync(key)
      return true
    } catch (e) {
      console.error(e)
      return false
    }
  }

  clear(): Promise<void> {
    return wxapi.clearStorage()
  }

  clearSync(): boolean {
    try {
      wx.clearStorageSync()
      return true
    } catch (e) {
      console.error(e)
      return false
    }
  }
}
