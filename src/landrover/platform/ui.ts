// @flow
import wxapi from '../../utils/wxapp-promise'

export default class UI {
  showToast(title: string, icon = 'none', duration = 2000): Promise<void> {
    return wxapi.showToast({
      title,
      icon,
      duration
    })
  }

  showLoading(title = '数据加载中', mask = false): Promise<void> {
    return wxapi.showLoading({ title, mask })
  }

  hideLoading(): Promise<void> {
    return wxapi.hideLoading()
  }

  showModal(
    title: string,
    content: string,
    showCancel = true,
    cancelText = '取消',
    confirmText = '确认'
  ): Promise<{ confirm: boolean; cancel: boolean }> {
    const cancelColor = '#ABABAB'
    const confirmColor = '#FE3925'
    return wxapi.showModal({
      title,
      content,
      showCancel,
      cancelText,
      cancelColor,
      confirmText,
      confirmColor
    })
  }
}
