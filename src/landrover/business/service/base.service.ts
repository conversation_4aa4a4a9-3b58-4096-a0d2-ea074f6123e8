import Taro from '@tarojs/taro';
import { pageReload } from '@utils/index';
import { config, container } from '../index';

export default class Service {
  baseUrl: BaseUrlTypes;

  responsePackFormat: ResponsePackFormat;

  constructor() {
    this.responsePackFormat = 'new';
  }

  setup(): Promise<void> {
    return Promise.resolve();
  }

  url(path: string): string {
    return `${this.baseUrl[config.env]}${path}`;
  }

  getENV(): string {
    return config.env;
  }

  getHeader(header: object, key: string) {
    for (const [k, v] of Object.entries(header)) {
      if (k.toLowerCase() === key.toLowerCase()) return v;
    }
    return '';
  }

  formatRequestInfo(data?: object, header?: object, authForCookie: boolean = true) {
    let extConfig = Taro.getExtConfigSync ? Taro.getExtConfigSync() : {};
    const auth = container.userService.auth;
    const defaultHeader = {
      'x-scene': 'MINIPROGRAM',
      'x-sign': extConfig.sign,
      Cookie: authForCookie && auth && auth.cookie ? auth.cookie : '',
    };

    const finalData: any = data instanceof Array ? data : Object.assign({}, data);
    const finalHeader: any = Object.assign(defaultHeader, header);

    Object.keys(finalData).forEach(key => (finalData[key] === null || finalData[key] === undefined || finalData[key] === '') && delete finalData[key]);
    Object.keys(finalHeader).forEach(key => (finalHeader[key] === null || finalHeader[key] === undefined || finalHeader[key] === '') && delete finalHeader[key]);

    return { finalData, finalHeader };
  }

  async request(path: string, method: RequestMethod, data?: object, header?: object, authForCookie = true, getCookie = false): Promise<void> {
    const url = this.url(path);
    const { finalData, finalHeader } = this.formatRequestInfo(data, header, authForCookie);
    finalHeader['Content-Type'] = 'application/json';
    const promise = Taro.request({ url, method, data: finalData, header: finalHeader });
    return this.unpackResponse(promise, getCookie);
  }

  async uploadFile(path: string, filePath: string, name = 'file', data?: object, header?: object): Promise<void> {
    const url = this.url(path);
    const { finalData, finalHeader } = this.formatRequestInfo(data, header, true);
    finalHeader['Content-Type'] = 'multipart/form-data';
    const promise = Taro.uploadFile({ url, filePath, name, formData: finalData, header:finalHeader });
    return this.unpackResponse(promise, false);
  }

  async unpackResponse(promise: Promise<any>, getCookie: boolean): Promise<any> {
    return promise.then(res => {
      const wxData: any = res.data.response ? res.data.response : res.data;
      const wxStatusCode = res.statusCode;
      let getWxData: any;
      // 二进制图片处理
      if (this.getHeader(res.header, 'Content-Type') === 'image/jpeg') {
        getWxData = { data: res.data, errorCode: 0, errorMessage: '', success: true };
      } else {
        getWxData = typeof wxData === 'string' ? JSON.parse(wxData) : wxData;
      }

      const { errorCode, data } = getWxData;
      // 2XX, 3XX 成功
      // 目前有种情况就是 状态码 返回为 200 的时候, 服务端还是会返回出错
      if (wxStatusCode < 400) {
        if (errorCode != 0) {
          return Promise.reject(getWxData);
        }
        if (getCookie) {
          return Promise.resolve({ ...data, cookie: this.getHeader(res.header, 'set-cookie').replace(/,/g, ';') });
        }
        return data;
      }
      return Promise.reject(wxData);
    })
    .catch(err => {
      if (err.errorCode === 401) {
        container.userService.clearUserInfo();
        // 登录失效，重新登录
        wx.login({
          success: ({ code }) => {
            if (!code) return;
            container.userService.getWxAuthLogin({ code }).then(() => pageReload());
          },
          fail: () => {
          },
        });
      }
      // 这里是小程序 iOS 的一个 bug， 如果返回体无法被 json 解析，就会抛出这个异常
      if (err.message === 'request:fail response data convert to UTF8 fail') {
        return;
      }
      return Promise.reject(err);
    });
  }
}
