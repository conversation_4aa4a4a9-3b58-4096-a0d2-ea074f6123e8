// @flow
import Service from './base.service'
import { storage } from '../index'
import Taro from '@tarojs/taro';

export default class UserService extends Service {

  constructor() {
    super()
    let extConfig = Taro.getExtConfigSync ? Taro.getExtConfigSync() : {};
    this.baseUrl = {
      dev: extConfig.domain + '/api',
      prd: extConfig.domain + '/api'
    }
  }

  /**
   * 登录鉴权对象
   *
   * @type {(Auth | null)}
   * @memberof UserService
   */
  auth: Auth | null = null

  /**
   * 账号用户信息
   *
   * @type {UserInfo}
   * @memberof UserService
   */
  userInfo: UserInfoTypes | null = null

  

  /**
   * 当前鉴权对象是否有效
   * accessToken 是否存在 expireIn 在有效期内
   * 当前的过期时间为真实过期时间前一天
   *
   * @returns {boolean}
   * @memberof UserService
   */
  isAuthAvailable(): boolean {
    const currentDate = new Date()
    const currentTime = currentDate.getTime()
    const auth = this.getUserInfo()
    if (auth != null) {
      const expireTime = auth.expireIn
      // 在过期时间前一天设置为过期标识
      return currentTime < expireTime
    } else {
      this.clearUserInfo()
      return false
    }
  }

  /**
   * 通过过期时间量计算过期的时间戳
   *
   * @param {number} day 过期时间量
   * @returns {number} 过期时间戳
   * @memberof UserService
   */
  p_timestampFromNowWithDay(day: number): number {
    const currentDate = new Date()
    const expireDate = new Date(currentDate)

    expireDate.setDate(currentDate.getDate() + day)
    const expireMillis = expireDate.getTime()
    return expireMillis
  }

  getUserInfo(): UserInfo | null {
    return this.loadUserInfo()
  }

  loadUserInfo(): UserInfo | null {
    const userInfoJSONString = storage.getItemSync('auth')

    if (userInfoJSONString != null && userInfoJSONString.length > 0) {
      const userInfo = JSON.parse(userInfoJSONString)

      this.auth = userInfo || null

      return userInfo
    } else {
      if (userInfoJSONString == null) {
        console.error('同步获取 auth 出错')
      }

      return null
    }
  }

  saveUserInfo(): void {
    const auth = this.auth
    const userInfoJSONString = JSON.stringify(auth)
    if (!storage.setItemSync('auth', userInfoJSONString)) {
      console.error('同步设置 auth 出错')
    }
  }

  clearUserInfo(): void {
    // TODO 兼容处理,为了测试,暂时删除 auth dev_auth gqc_auth
    if (
      storage.removeItemSync('auth') ||
      storage.removeItemSync('dev_auth') ||
      storage.removeItemSync('gqc_auth')
    ) {
      this.auth = null
      this.userInfo = null
    } else {
      console.error('同步删除 auth 出错')
    }
  }
}
