declare interface PositionTypes {
  /** 职位ID */
  id: number;
  /** 客户ID */
  clientId: number;
  clientDisplayName?: string;
  /** 职位名称 */
  name: string;
  /** 薪资类型：0-未知，1-年薪，2-月薪，3-周薪，4-日薪，5-时薪 */
  salaryType: number;
  /** 最低薪资，单位元 */
  salaryBegin: number;
  /** 最高薪资，单位元 */
  salaryEnd: number;
  /** 工作性质：1-全职，2-兼职，3-实习 */
  workType: number;
  /** 招聘人数 */
  number: number;
  /** 需求开始时间 */
  demandStartAt: string;
  /** 需求过期时间，为空则表示长期有效 */
  demandExpireAt: string;
  /** 学历要求 */
  degree: number;
  /** 招聘类别：内招/猎头/外包/RPO */
  recruitmentType: number;
  /** 工作年限：不限/1年以内/1-3年/3-5年/5-10年/10年以上 */
  workYears: number;
  /** 工作城市 */
  city: number[];
  /** 工作详细地址 */
  address: string;
  /** 职位描述 */
  description: string;
  /** 职位要求 */
  requirement: string;
  /** 职位亮点 */
  advantages: string[];
  // 职位工作环境
  galleries?: { name: string, url: string }[];
  /** 是否热门职位 */
  isHot: number;
  /** 是否置顶职位 */
  isTop: number;
  /** 面试时间，自由录入 */
  interviewTime: string;
  /** 面试地点 */
  interviewAddress: string;
  /** 面试联系人 */
  interviewer: {
    name: string
    phone: string
  } | null;
  /** 职位状态：进行中/已完成/已暂停/已取消 */
  status: number;
  /** 流程节点列表 */
  flow?: { [key: string]: any };
  flowNodes: number[];
  /** 供应商委托费用 */
  supplierFee: string;
  /** 职位负责人ID */
  ownerId: number;
  /** 创建人ID */
  creatorId: number;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
  /** 客户信息 */
  client: {
    /** 客户ID */
    id: number;
    /** 客户名称 */
    name: string;
    shortName: string;
    logo?: {
      /** 文件URL */
      url: string;
      /** 文件名称 */
      name?: string;
    };
    addressCoord: string;
    address: string;
    /** 客户工作环境 */
    galleries?: { name: string, url: string }[];
  };
  /** 创建人信息 */
  creator: {
    /** 创建人ID */
    id: number;
    /** 创建人姓名 */
    name: string;
  };
  /** 职位负责人信息 */
  owner: {
    /** 负责人ID */
    id: number;
    /** 负责人姓名 */
    name: string;
  };
  tenant: {
    wecom?: { [key: string]: any }
  };
  /** 协作者 */
  participants: {
    /** 用户ID */
    id: number;
    /** 用户名 */
    name: string;
  }[];
  supplierOpen: 0 | 1;
  /** 流程节点数据 */
  flowSummary: { [key: string]: any }[];
  /** 数据权限类型：READ-只读，OWN-拥有 */
  __PERMIT__: string;
  fav: { id: number } | null;
}

declare interface FavsTypes {
  createdAt: string;
  id: number;
  tenantId: number;
  position: PositionTypes;
  positionId: number;
  candidateId: number;
  userId: number;
}

// @ts-ignore
declare interface ClientTypes {
  galleries?: { name: string, url: string }[];
  videos?: { name: string, url: string }[];
  addressCoord: string;
  /** 客户ID */
  id: number;
  /** 客户名称 */
  name: string;
  /** 客户简称 */
  shortName: string;
  /** 客户简介 */
  intro: string;
  /** 客户LOGO */
  logo?: {
    /** 文件URL */
    url: string;
    /** 文件名称 */
    name?: string;
  };
  /** 营业执照 */
  license?: {
    /** 文件URL */
    url: string;
    /** 文件名称 */
    name?: string;
  };
  /** 客户网址 */
  website: string;
  /** 所在城市，从顶级ID到子ID */
  city: number[];
  /** 客户规模 */
  scale: number;
  /** 客户性质：0-未知，1-外资，2-合资，3-国企，4-私企，5-政府机关，6-事业单位 */
  nature: number;
  /** 所属行业，从顶级ID到子ID */
  industry: number[];
  /** 客户地址 */
  address: string;
  /** 客户亮点列表 */
  advantages: string[];
  /** 备注 */
  comment: string;
  /** 审核状态：0-待审核，1-已通过，2-已拒绝 */
  auditStatus: number;
  /** 审核备注 */
  auditNote: string;
  /** 合作类型：0-未知，1-自营，2-托管 */
  cooperateType: number;
  /** 合作状态：0-待开发，1-已签约，2-停止合作 */
  cooperateStatus: number;
  /** 母子类型：0-独立公司，1-子公司，2-母公司 */
  childMotherType: number;
  /** 母公司ID */
  motherId: number;
  /** 签约时间 */
  signedAt?: string;
  /** 创建人ID */
  creatorId: number;
  /** 客户负责人ID */
  ownerId: string;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
  /** 客户总职位数 */
  positionCount: number;
  /** 客户进行中职位数 */
  openPositionCount: number;
  /** 创建人信息 */
  creator: {
    /** 创建人ID */
    id: string;
    /** 创建人姓名 */
    name: string;
  };
  /** 客户负责人信息 */
  owner: {
    /** 负责人ID */
    id: number;
    /** 负责人姓名 */
    name: string;
  };
  /** 共享的用户 */
  sharedUsers: {
    /** 用户ID */
    id: number;
    /** 用户名 */
    name: string;
  }[];
  /** 共享的团队 */
  sharedTeams: {
    /** 团队ID */
    id: number;
    /** 团队名称 */
    name: string;
  }[];
  /** 联系人列表 */
  contacts: {
    /** 联系人ID */
    id: number;
    /** 联系人姓名 */
    name: string;
  }[];
  /** 合同列表 */
  contracts: {
    /** 合同ID */
    id: number;
    /** 合同名称 */
    name: string;
  }[];
  /** 发票列表 */
  invoices: {
    /** 发票ID */
    id: number;
    /** 发票名称 */
    name: string;
  }[];
  /** 发票接收人列表 */
  invoiceReceivers: {
    /** 发票接收人ID */
    id: number;
    /** 发票接收人名称 */
    name: string;
  }[];
  /** 数据权限类型：READ-只读，OWN-拥有 */
  __PERMIT__: string;
}

declare interface ResumeTypes {
  /** 简历ID */
  id: number;
  /** 姓名 */
  name: string;
  /** 头像 */
  avatar: {
    /** 文件URL */
    url: string;
    /** 文件名称 */
    name?: string;
  };
  /** 性别 */
  gender: number;
  /** 学历 */
  degree: number;
  /** 出生年月 */
  birth: string;
  /** 身份证 */
  card: string;
  tags: {
    id: number;
    name: string;
  }[];
  /** 籍贯 */
  native: number[];
  /** 邮箱 */
  email: string;
  /** 手机 */
  phone: string;
  /** QQ号 */
  qq: string;
  /** 微信 */
  wechat: string;
  /** 现居地（城市） */
  address: number[];
  /** 身高（CM） */
  height: number;
  /** 体重（KG） */
  weight: number;
  /** 民族 */
  nation: number;
  /** 婚姻状态：0-未婚，1-已婚 */
  marital: number;
  /** 驾照类型 */
  driverLicense: number;
  /** 驾龄 */
  drivingYears: number;
  /** 是否有不良记录：0-无，1-有 */
  hasBadRecords: number;
  /** 有无传染病或重疾史：0-无，1-有 */
  hasDisease: number;
  /** 求职状态 */
  currentStatus: number;
  /** 期望城市ID列表 */
  expectCities: number[];
  /** 期望行业ID列表 */
  expectIndustries: number[];
  /** 期望职位 */
  expectPosition: string;
  /** 期望薪资，不限格式 */
  expectSalary: string;
  /** 自我评价 */
  selfRemark: string;
  /** 简历附件信息，详见文件API文档 */
  attachment: {
    /** 文件URL */
    url: string;
    /** 文件名称 */
    name: string;
  };
  /** 简历来源 */
  source: number;
  /** 创建人ID */
  creatorId: number;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
  /** 教育经历列表 */
  educations: EducationTypes[];
  /** 工作经历列表 */
  works: WorkTypes[];
  /** 项目经理列表 */
  projects: ProjectTypes[];
  /** 语言能力列表 */
  languages: LanguageTypes[];
  /** 投递记录列表 */
  flows: {
    /** 投递记录ID */
    id: number;
    /** 职位ID */
    positionId: number;
  }[];
}

declare interface FlowTypes {
  /** 投递记录ID */
  id: number;
  /** 职位ID */
  positionId: number;
  /** 简历ID */
  resumeId: number;
  /** 简历信息，详见简历接口文档 */
  resume: { [key: string]: any };

  /** 投递方式 */
  deliveryMode: number;
  /** 当前流转节点ID */
  currentNode: number;
  /** 创建人ID */
  creatorId: number;
  /** 投递时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
  position: { [key: string]: any };
}

declare interface ListResponseTypes {
  current: number;
  list: any[];
  pageSize: number;
  total: number;
}

