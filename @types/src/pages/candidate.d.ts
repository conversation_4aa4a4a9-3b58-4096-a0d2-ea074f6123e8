declare interface EducationTypes {
  /** 教育经历ID */
  id?: number;
  /** 简历ID */
  resumeId?: number;
  /** 学校名称 */
  school?: string;
  /** 学历 */
  degree?: number;
  /** 专业名称 */
  discipline?: string;
  /** 入学时间 */
  startTime?: string;
  /** 毕业时间 */
  endTime?: string;
  /** 毕业时间是否至今 */
  soFar?: number;
  /** 是否统招：0-是，1-否 */
  isEntrance?: number;
  /** 创建人ID */
  creatorId?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
}

declare interface WorkTypes {
  /** 工作经历ID */
  id?: number;
  /** 简历ID */
  resumeId?: number;
  /** 公司名称 */
  company?: string;
  /** 职位名称 */
  position?: string;
  /** 部门名称 */
  department?: string;
  /** 公司规模 */
  scale?: number;
  /** 公司类型 */
  nature?: number;
  /** 汇报对象 */
  reportingTo?: string;
  /** 下属人数 */
  subordinatesCount?: number;
  /** 职级 */
  rank?: string;
  /** 薪资情况 */
  salary?: string;
  /** 入职时间 */
  startTime?: string;
  /** 离职时间 */
  endTime?: string;
  /** 离职时间是否至今 */
  soFar?: number;
  /** 工作内容 */
  description?: string;
  /** 工作业绩 */
  achievements?: string;
  /** 创建人ID */
  creatorId?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
}

declare interface ProjectTypes {
  /** 项目经历ID */
  id?: number;
  /** 简历ID */
  resumeId?: number;
  /** 项目名称 */
  name?: string;
  /** 所在公司 */
  company?: string;
  /** 所属职位 */
  position?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 结束时间是否至今 */
  soFar?: number;
  /** 项目描述 */
  description?: string;
  /** 项目职责 */
  responsibilities?: string;
  /** 项目业绩 */
  achievements?: string;
  /** 创建人ID */
  creatorId?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
}

declare interface LanguageTypes {
  /** 语言能力ID */
  id?: number;
  /** 简历ID */
  resumeId?: number;
  /** 语言名称 */
  name?: string;
  /** 语言能力 */
  level?: number;
  /** 创建人ID */
  creatorId?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
}

declare interface AttachmentTypes {
  /** 附件ID */
  id?: number;
  /** 简历ID */
  resumeId?: number;
  /** 附件类型 */
  type?: string;
  /** 附件名称 */
  name?: string;
  /** 附件地址 */
  url?: string;
  /** 创建人 */
  creatorId?: number;
  /** 创建时间 */
  createdAt?: string;
}

declare interface ResumeTypes {
  /** 简历ID */
  id: number;
  /** 供应商简历ID */
  supplierResumeId: number;
  /** 租户ID */
  tenantId: number;
  /** 姓名 */
  name: string;
  /** 头像 */
  avatar: {
    /** 文件URL */
    url: string;
    /** 文件名称 */
    name?: string;
  };
  /** 性别 */
  gender: number;
  /** 学历 */
  degree: number;
  /** 毕业学校 */
  school: string;
  /** 出生年月 */
  birth: string;
  /** 身份证 */
  card: string;
  /** 籍贯 */
  native: number[];
  /** 邮箱 */
  email: string;
  /** 手机 */
  phone: string;
  /** QQ号 */
  qq: string;
  /** 微信 */
  wechat: string;
  /** 现居地（城市） */
  address: number[];
  /** 身高（CM） */
  height: number;
  /** 体重（KG） */
  weight: number;
  /** 民族 */
  nation: number;
  /** 婚姻状态：0-未婚，1-已婚 */
  marital: number;
  /** 驾照类型 */
  driverLicense: number;
  /** 驾龄 */
  drivingYears: number;
  /** 是否有不良记录：0-无，1-有 */
  hasBadRecords: number;
  /** 有无传染病或重疾史：0-无，1-有 */
  hasDisease: number;
  /** 求职状态 */
  currentStatus: number;
  /** 期望城市ID列表 */
  expectCities: number[];
  /** 期望行业ID列表 */
  expectIndustries: number[];
  /** 期望职位 */
  expectPosition: string;
  /** 期望薪资，不限格式 */
  expectSalary: string;
  /** 自我评价 */
  selfRemark: string;
  /** 简历来源 */
  source: number;
  /** 是否锁定状态：0-否，1-是 */
  isLock: number;
  /** 锁定方式：0-自动，1-手动 */
  lockType: number;
  /** 锁定操作人ID */
  lockUserId: number;
  /** 拥有者ID */
  ownerId: number;
  /** 创建人ID */
  creatorId: number;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
  /** 工作年限 */
  workYears: string;
  /** 毕业时间 */
  graduateTime: string;
  /** 教育经历列表 */
  educations: EducationTypes[];
  /** 工作经历列表 */
  works: WorkTypes[];
  /** 项目经历列表 */
  projects: ProjectTypes[];
  /** 语言能力列表 */
  languages: LanguageTypes[];
  /** 附件列表 */
  attachments: AttachmentTypes[];
  /** 投递记录列表 */
  flows: {
    /** 投递记录ID */
    id: number;
    /** 职位ID */
    positionId: number;
  }[];
  /** 简历标签列表 */
  tags: {
    /** 简历标签ID */
    id: number;
    /** 标签名称 */
    name: string;
  }[];
  /** 简历文件夹信息 */
  folder?: {
    /** 简历文件夹ID */
    id: number;
    /** 简历文件夹名称 */
    name: string;
  };
  /** 创建人信息 */
  creator: {
    /** 创建人ID */
    id: number;
    /** 创建人姓名 */
    name: string;
  };
  /** 拥有者信息 */
  owner: {
    /** 拥有者ID */
    id: number;
    /** 拥有者姓名 */
    name: string;
  };
  /** 锁定操作人信息 */
  lockUser?: {
    /** 锁定人ID */
    id: number;
    /** 锁定人姓名 */
    name: string;
  };
  /** 共享的用户 */
  sharedUsers: {
    /** 用户ID */
    id: number;
    /** 用户姓名 */
    name: string;
  }[];
  /** 共享的团队 */
  sharedTeams: {
    /** 团队ID */
    id: number;
    /** 团队姓名 */
    name: string;
  }[];
  /** 数据权限类型：READ-只读，OWN-拥有 */
  __PERMIT__: string;
}
