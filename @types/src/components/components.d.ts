interface FunctionComponentStatics<P = {}> {
  propTypes: React.ValidationMap<P>
  contextTypes: React.ValidationMap<any>
  defaultProps: Partial<P>
  displayName: string
  externalClasses: string[]
}

declare type FixFunctionComponent<
  P extends object = {},
  StaticKeys extends keyof FunctionComponentStatics<P> = never,
  externalKeys extends keyof FunctionComponentStatics<P> = never
> = {
  (
    props: P & { children?: React.ReactNode },
    context?: any,
  ): React.ReactElement<any> | null
} & { [K in StaticKeys]: FunctionComponentStatics<P>[K] } &
  { [K in externalKeys]: FunctionComponentStatics<P>[K] }
