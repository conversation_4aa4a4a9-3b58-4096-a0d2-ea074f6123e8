declare type UserInfoTypes = {
  /** 供应商用户ID */
  id: number;
  /** 供应商商户ID */
  supplierId: number;
  supplier?: {
    /** 需求方ID */
    id: number;
    /** 用户类型：1-供应商、2-领队 */
    type: number;
    /** 组织名/公司名 */
    orgName: string;
    /** 是否可用：0-禁用，1-可用；禁用则不可登录 */
    active: number;
    /** 唯一编号，上游添加使用 */
    uniqueCode: string;
    /** 创建时间 */
    createdAt: string;
    /** 更新时间 */
    updatedAt: string;
  };
  avatar?: string;
  gender?: number;
  /** 登录手机号 */
  phone: string;
  /** 姓名 */
  name: string;
  /** 是否可用：0-禁用，1-可用 */
  active: number;
  /** 最后登录IP */
  lastLoginTime: string;
  /** 最后登录IP（冗余字段） */
  lastLoginIp: string;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
}
