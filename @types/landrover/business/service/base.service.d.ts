declare type RequestMethod =
  | 'GET'
  | 'POST'
  | 'PUT'
  | 'DELETE'
  | 'OPTIONS'
  | 'HEAD'
  | 'TRACE'
  | 'CONNECT'

declare type ResponsePackFormat = 'legacy' | 'old' | 'new'

declare type ResponsePackage = {
  data?: any
  error?: {
    code: number
    message: string
    debugInfo: string
  }
  results?: object
  behavior?: {
    content: string
    urlScheme: string
    type: 'NOTICE' | 'ALERT' | 'TOAST'
  }
  err_no?: number
  err_msg?: string
}

declare type BaseUrlTypes = {
  dev: string
  prd: string
}
