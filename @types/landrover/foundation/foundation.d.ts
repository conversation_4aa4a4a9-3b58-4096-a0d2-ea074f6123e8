declare interface StorageVirtualClass {
  setItem(key: string, value: string | number | object): Promise<void>
  setItemSync(key: string, value: string | number | object): boolean

  getItem(key: string): Promise<string | object>
  getItemSync(key: string): void

  removeItem(key: string): Promise<void>
  removeItemSync(key: string): boolean

  clear(): Promise<void>
  clearSync(): boolean
}

declare interface RequestVirtualClass {
  request(url: string, method: RequestMethod, data: object, header: object): Promise<object>
}
